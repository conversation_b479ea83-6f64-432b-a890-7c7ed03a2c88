<?php
/**
 * 微信小程序H5跳转工具 - 专业版
 * 基于微信官方URL Scheme和URL Link方案
 * 支持明文URL Scheme、加密URL Scheme、加密URL Link三种跳转方式
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024
 */

// 引入配置文件和类文件
require_once 'config.php';
require_once 'WechatMiniProgram.php';

// 小程序配置信息
define('APPID', WECHAT_APPID);
define('APP_SECRET', WECHAT_APP_SECRET);

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json; charset=utf-8');

    try {
        $wechat = new WechatMiniProgram(APPID, APP_SECRET);
        $path = $_POST['path'] ?? '';
        $queryParams = [];

        // 解析查询参数
        if (!empty($_POST['query'])) {
            parse_str($_POST['query'], $queryParams);
        }

        $envVersion = $_POST['env_version'] ?? 'release';
        $type = $_POST['type'] ?? 'plain';
        $results = [];

        switch ($type) {
            case 'plain':
                $results['plain_scheme'] = $wechat->generatePlainUrlScheme($path, $queryParams, $envVersion);
                break;
            case 'encrypted_scheme':
                $results['encrypted_scheme'] = $wechat->generateEncryptedUrlScheme($path, $queryParams, $envVersion);
                break;
            case 'encrypted_link':
                $results['encrypted_link'] = $wechat->generateEncryptedUrlLink($path, $queryParams, $envVersion);
                break;
            case 'all':
                $results['plain_scheme'] = $wechat->generatePlainUrlScheme($path, $queryParams, $envVersion);
                $results['encrypted_scheme'] = $wechat->generateEncryptedUrlScheme($path, $queryParams, $envVersion);
                $results['encrypted_link'] = $wechat->generateEncryptedUrlLink($path, $queryParams, $envVersion);
                break;
        }

        echo json_encode(['success' => true, 'data' => $results]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小程序跳转工具</title>
    <meta name="description" content="基于微信官方API的小程序H5跳转工具">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f7f7f7;
            color: #333;
            padding-bottom: 60px;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            body {
                padding-bottom: 40px;
                font-size: 14px;
            }
        }

        .container {
            max-width: 750px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
        }

        /* 移动端容器适配 */
        @media (max-width: 768px) {
            .container {
                max-width: 100%;
                margin: 0;
            }
        }

        .page {
            display: none;
            padding: 20px;
            padding-bottom: 80px;
        }

        /* 移动端页面适配 */
        @media (max-width: 768px) {
            .page {
                padding: 12px;
                padding-bottom: 60px;
            }
        }

        .page.active {
            display: block;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #e5e5e5;
            margin-bottom: 20px;
        }

        /* 移动端标题适配 */
        @media (max-width: 768px) {
            .page-title {
                font-size: 16px;
                padding: 16px 0;
                margin-bottom: 16px;
            }
        }

        .form-item {
            margin-bottom: 20px;
        }

        .form-item label {
            display: block;
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .form-item input,
        .form-item select,
        .form-item textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #e5e5e5;
            border-radius: 6px;
            font-size: 16px;
            background: #fafafa;
        }

        .form-item input:focus,
        .form-item select:focus,
        .form-item textarea:focus {
            outline: none;
            border-color: #07c160;
            background: white;
        }

        .form-item textarea {
            height: 80px;
            resize: vertical;
        }

        .form-item small {
            display: block;
            color: #999;
            font-size: 12px;
            margin-top: 5px;
        }

        .btn-primary {
            width: 100%;
            background: #07c160;
            color: white;
            border: none;
            padding: 15px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 20px;
        }

        .btn-primary:hover {
            background: #06ad56;
        }

        .btn-primary:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .result-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .result-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .result-url {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin-bottom: 10px;
            color: #666;
        }

        .result-actions {
            display: flex;
            gap: 8px;
        }

        .btn-small {
            flex: 1;
            padding: 8px 12px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            text-align: center;
        }

        .btn-copy {
            background: #007aff;
            color: white;
        }

        .btn-test {
            background: #34c759;
            color: white;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #07c160;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 12px;
            border-radius: 6px;
            margin: 15px 0;
            font-size: 14px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 32px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            transform: scale(0.8) translateY(20px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.8);
            position: relative;
            overflow: hidden;
        }

        .modal-overlay.show .modal-content {
            transform: scale(1) translateY(0);
        }

        .modal-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #07c160, #06ad56, #059649);
        }

        .modal-header {
            text-align: center;
            margin-bottom: 24px;
        }

        .modal-icon {
            font-size: 48px;
            margin-bottom: 16px;
            display: block;
            filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
        }

        .modal-title {
            font-size: 20px;
            font-weight: 700;
            color: #2c3e50;
            margin: 0 0 8px 0;
            letter-spacing: -0.5px;
        }

        .modal-message {
            font-size: 16px;
            color: #5a6c7d;
            line-height: 1.5;
            margin: 0;
            text-align: center;
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            margin-top: 24px;
            justify-content: center;
        }

        .modal-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 100px;
            position: relative;
            overflow: hidden;
        }

        .modal-btn-primary {
            background: linear-gradient(135deg, #07c160 0%, #06ad56 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(7, 193, 96, 0.25);
        }

        .modal-btn-primary:hover {
            background: linear-gradient(135deg, #06ad56 0%, #059649 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(7, 193, 96, 0.4);
        }

        .modal-btn-secondary {
            background: #f8f9fa;
            color: #5a6c7d;
            border: 1px solid #e9ecef;
        }

        .modal-btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-1px);
        }

        /* 移动端模态框适配 */
        @media (max-width: 768px) {
            .modal-content {
                padding: 24px;
                border-radius: 16px;
                margin: 20px;
            }

            .modal-icon {
                font-size: 40px;
                margin-bottom: 12px;
            }

            .modal-title {
                font-size: 18px;
            }

            .modal-message {
                font-size: 15px;
            }

            .modal-actions {
                flex-direction: column;
                gap: 8px;
            }

            .modal-btn {
                width: 100%;
                padding: 14px 24px;
                font-size: 15px;
            }
        }

        /* 任务信息卡片样式 */
        .task-info-card {
            background: linear-gradient(135deg, #f8fffe 0%, #f0fff8 100%);
            border: 1px solid #e8f5e8;
            border-radius: 16px;
            padding: 20px;
            margin-top: 16px;
            box-shadow: 0 4px 16px rgba(7, 193, 96, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .task-info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #07c160, #06ad56, #059649);
        }

        .task-info-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            gap: 12px;
        }

        .task-info-icon {
            font-size: 24px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .task-info-title {
            font-size: 18px;
            font-weight: 700;
            color: #2c3e50;
            margin: 0;
            letter-spacing: -0.3px;
        }

        .task-info-content {
            margin-bottom: 20px;
        }

        .task-info-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(7, 193, 96, 0.1);
            font-size: 14px;
        }

        .task-info-item:last-child {
            border-bottom: none;
        }

        .task-info-label {
            color: #666;
            min-width: 80px;
            font-weight: 500;
        }

        .task-info-value {
            color: #333;
            font-weight: 600;
            flex: 1;
        }

        .task-info-status {
            color: #07c160;
            font-weight: 600;
        }

        .task-info-tip {
            background: rgba(7, 193, 96, 0.05);
            border: 1px solid rgba(7, 193, 96, 0.2);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 13px;
            color: #059649;
            line-height: 1.4;
        }

        .task-info-tip strong {
            color: #07c160;
        }

        .ad-jump-btn {
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            color: white;
            border: none;
            padding: 14px 24px;
            border-radius: 12px;
            font-size: 15px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 16px rgba(24, 144, 255, 0.25);
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
            min-width: 120px;
            justify-content: center;
            width: 100%;
        }

        .ad-jump-btn::before {
            content: "📺";
            font-size: 14px;
            filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2));
        }

        .ad-jump-btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s;
        }

        .ad-jump-btn:hover {
            background: linear-gradient(135deg, #096dd9 0%, #0050b3 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(24, 144, 255, 0.4);
        }

        .ad-jump-btn:hover::after {
            left: 100%;
        }

        .ad-jump-btn:active {
            transform: translateY(0);
            box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
        }

        /* 移动端任务信息卡片适配 */
        @media (max-width: 768px) {
            .task-info-card {
                padding: 16px;
                border-radius: 12px;
                margin-top: 12px;
            }

            .task-info-header {
                margin-bottom: 12px;
                gap: 8px;
            }

            .task-info-icon {
                font-size: 20px;
            }

            .task-info-title {
                font-size: 16px;
            }

            .task-info-content {
                margin-bottom: 16px;
            }

            .task-info-item {
                padding: 6px 0;
                font-size: 13px;
            }

            .task-info-label {
                min-width: 70px;
            }

            .task-info-tip {
                padding: 10px;
                font-size: 12px;
                margin-bottom: 12px;
            }

            .ad-jump-btn {
                padding: 12px 20px;
                font-size: 14px;
                border-radius: 10px;
            }

            .ad-jump-btn::before {
                font-size: 12px;
            }
        }

        .info-card {
            background: #e7f3ff;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #0c5460;
        }

        .about-content {
            line-height: 1.6;
        }

        .about-content h3 {
            margin: 20px 0 10px 0;
            color: #333;
            font-size: 16px;
        }

        .about-content p {
            margin-bottom: 15px;
            color: #666;
            font-size: 14px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
            color: #666;
        }

        .feature-list li:before {
            content: "✓";
            color: #07c160;
            font-weight: bold;
            margin-right: 8px;
        }

        /* 底部导航栏 */
        .tabbar {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 750px;
            height: 60px;
            background: white;
            border-top: 1px solid #e5e5e5;
            display: flex;
            z-index: 1000;
        }

        .tabbar-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: color 0.2s;
            text-decoration: none;
            color: #999;
        }

        .tabbar-item.active {
            color: #07c160;
        }

        .tabbar-item img {
            width: 24px;
            height: 24px;
            margin-bottom: 2px;
        }

        .tabbar-item span {
            font-size: 10px;
        }

        /* 移动端底部导航适配 */
        @media (max-width: 768px) {
            .tabbar {
                height: 50px;
                padding: 0 8px;
            }

            .tabbar-item {
                padding: 6px 8px;
            }

            .tabbar-item img {
                width: 20px;
                height: 20px;
                margin-bottom: 1px;
            }

            .tabbar-item span {
                font-size: 9px;
            }
        }

        .developer-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            text-align: center;
        }

        .developer-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 15px;
            display: block;
        }

        .developer-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .developer-desc {
            font-size: 12px;
            color: #999;
            margin-bottom: 15px;
        }

        .developer-links {
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .developer-links a {
            color: #007aff;
            text-decoration: none;
            font-size: 12px;
        }

        /* 账号管理弹窗样式 */
        .account-switch-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            z-index: 9999;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .account-switch-container {
            width: 90%;
            max-width: 350px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            padding: 30px 20px 20px;
            position: relative;
        }

        /* 移动端弹窗适配 */
        @media (max-width: 768px) {
            .account-switch-container {
                width: 95%;
                max-width: 320px;
                border-radius: 16px;
                padding: 24px 16px 16px;
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            }
        }

        .account-switch-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .account-switch-title {
            margin: 0;
            font-size: 22px;
            font-weight: 600;
            background: linear-gradient(135deg, #07c160, #06ad56);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 15px;
        }

        .account-switch-subtitle {
            margin: 5px 0 0;
            color: #666;
            font-size: 14px;
        }

        /* 移动端弹窗标题适配 */
        @media (max-width: 768px) {
            .account-switch-header {
                margin-bottom: 20px;
            }

            .account-switch-title {
                font-size: 20px;
                margin-bottom: 12px;
            }

            .account-switch-subtitle {
                font-size: 13px;
            }
        }

        .account-list-wrapper {
            background-color: #f9fafb;
            border-radius: 12px;
            padding: 8px;
            margin-bottom: 15px;
        }

        .account-list {
            max-height: 230px;
            overflow-y: auto;
        }

        .account-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 12px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .account-item:not(:last-child) {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .account-item:hover {
            background-color: #f0f2f5;
        }

        .account-info {
            flex: 1;
            display: flex;
            align-items: center;
        }

        .account-text-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .account-text {
            font-size: 15px;
            font-weight: 500;
            color: #333;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 170px;
        }

        .switch-text {
            font-size: 12px;
            color: #07c160;
            margin-left: 10px;
            padding: 2px 8px;
            background-color: rgba(7, 193, 96, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(7, 193, 96, 0.2);
            box-shadow: 0 1px 2px rgba(7, 193, 96, 0.05);
        }

        .delete-account-btn {
            background: none;
            border: none;
            color: #ff4d4f;
            cursor: pointer;
            padding: 8px;
            margin-left: 10px;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        }

        .delete-account-btn:hover {
            background-color: #ffecec;
        }

        .account-switch-close {
            position: absolute;
            top: 15px;
            right: 15px;
            cursor: pointer;
            color: #999;
            font-size: 20px;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s;
        }

        .account-switch-close:hover {
            background-color: #f5f5f5;
        }

        .add-account-form {
            margin-top: 15px;
            border-top: 1px solid #f2f2f2;
            padding-top: 15px;
        }

        .add-account-header {
            margin-bottom: 15px;
            text-align: center;
        }

        .add-account-title {
            margin: 0 0 15px;
            font-size: 18px;
            color: #333;
            font-weight: 600;
        }

        .add-account-input {
            width: 100%;
            height: 45px;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 10px;
            box-sizing: border-box;
            font-size: 15px;
            background-color: #f9f9fb;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
            margin-bottom: 15px;
        }

        .add-account-input:focus {
            outline: none;
            border-color: #07c160;
            background-color: white;
        }

        .add-account-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .add-account-btn {
            border: none;
            border-radius: 10px;
            padding: 12px 16px;
            font-size: 15px;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 80px;
        }

        .add-account-btn-cancel,
        .add-account-btn-save {
            flex: 1;
        }

        .add-account-btn-clear {
            flex: 0 0 auto;
        }

        .add-account-btn-cancel {
            background-color: #f5f5f5;
            color: #666;
        }

        .add-account-btn-clear {
            background-color: #ff9500;
            color: white;
            box-shadow: 0 4px 12px rgba(255, 149, 0, 0.15);
        }

        .add-account-btn-clear:hover {
            background-color: #e6850e;
        }

        .add-account-btn-save {
            background-color: #07c160;
            color: white;
            box-shadow: 0 4px 12px rgba(7, 193, 96, 0.15);
        }

        .add-account-btn:hover {
            opacity: 0.9;
        }

        .account-switch-footer {
            padding-top: 20px;
            padding-bottom: 5px;
            border-top: 1px solid #f2f2f2;
        }

        .add-new-account-btn {
            background-color: #07c160;
            color: white;
            border: none;
            border-radius: 25px;
            padding: 12px 20px;
            font-size: 15px;
            cursor: pointer;
            min-width: 60%;
            display: block;
            margin: 0 auto;
            transition: all 0.2s ease;
        }

        .add-new-account-btn:hover {
            background-color: #06ad56;
        }

        .empty-accounts {
            text-align: center;
            padding: 25px 0;
            color: #999;
            font-size: 14px;
        }

        /* 账号状态显示样式 */
        .account-status-container {
            text-align: center;
            padding: 30px 20px;
        }

        .no-accounts-message {
            color: #666;
            font-size: 16px;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .no-accounts-icon {
            font-size: 48px;
            color: #ddd;
            margin-bottom: 15px;
        }

        .add-first-account-btn {
            background: linear-gradient(135deg, #07c160, #06ad56);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(7, 193, 96, 0.3);
            transition: all 0.3s ease;
        }

        .add-first-account-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(7, 193, 96, 0.4);
        }

        .accounts-list-container {
            background: #f9fafb;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .accounts-list-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        /* 移动端账号列表标题适配 */
        @media (max-width: 768px) {
            .accounts-list-title {
                font-size: 14px;
                margin-bottom: 12px;
            }
        }

        .account-summary-item {
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
            border-radius: 20px;
            margin-bottom: 16px;
            border: 1px solid #e8ecf0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
            position: relative;
        }

        .account-summary-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #07c160 0%, #06ad56 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .account-summary-item:hover {
            border-color: #07c160;
            box-shadow: 0 8px 32px rgba(7, 193, 96, 0.15);
            transform: translateY(-4px);
        }

        .account-summary-item:hover::before {
            opacity: 1;
        }

        .account-info-section {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 24px;
            background: transparent;
        }

        /* 移动端账号卡片适配 */
        @media (max-width: 768px) {
            .account-summary-item {
                border-radius: 12px;
                margin-bottom: 12px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            }

            .account-info-section {
                padding: 16px 18px;
                flex-direction: row;
                gap: 12px;
            }
        }

        .account-summary-text {
            font-size: 18px;
            color: #1a1a1a;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .account-summary-text::before {
            content: "👤";
            font-size: 20px;
            opacity: 0.8;
        }

        .account-summary-badge {
            background: #07c160;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
        }

        .switch-account-btn {
            background: linear-gradient(135deg, #07c160 0%, #06ad56 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 12px rgba(7, 193, 96, 0.25);
            position: relative;
            overflow: hidden;
        }

        .switch-account-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .switch-account-btn:hover {
            background: linear-gradient(135deg, #06ad56 0%, #059649 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(7, 193, 96, 0.35);
        }

        .switch-account-btn:hover::before {
            left: 100%;
        }

        /* 移动端账号文本和按钮适配 */
        @media (max-width: 768px) {
            .account-summary-text {
                font-size: 16px;
                gap: 8px;
            }

            .account-summary-text::before {
                font-size: 18px;
            }

            .switch-account-btn {
                padding: 10px 16px;
                border-radius: 20px;
                font-size: 12px;
                box-shadow: 0 2px 8px rgba(7, 193, 96, 0.2);
            }
        }

        .step-input-container {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 20px 24px;
            background: linear-gradient(135deg, #f8fffe 0%, #f0fff8 100%);
            border-top: 1px solid #f0f0f0;
            transition: all 0.3s ease;
            position: relative;
        }

        .step-input-row {
            display: flex;
            align-items: center;
            gap: 16px;
            flex: 1;
        }

        .step-input-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 24px;
            right: 24px;
            height: 1px;
            background: linear-gradient(90deg, transparent, #e8e8e8, transparent);
        }

        .step-input-label {
            font-size: 16px;
            color: #2c3e50;
            font-weight: 700;
            min-width: 100px;
            display: flex;
            align-items: center;
            position: relative;
        }

        .step-input-label::before {
            content: "⚙️";
            margin-right: 8px;
            font-size: 18px;
            filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
        }

        .step-input {
            padding: 10px 12px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            flex: 1;
            text-align: left;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            color: #2c3e50;
        }

        .step-input:focus {
            outline: none;
            border-color: #07c160;
            box-shadow: 0 0 0 4px rgba(7, 193, 96, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: scale(1.05);
            background: #ffffff;
        }

        .step-input:hover {
            border-color: #07c160;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        /* 移动端Step输入区域适配 */
        @media (max-width: 768px) {
            .step-input-container {
                gap: 12px;
                padding: 16px 18px;
                flex-direction: column;
                align-items: stretch;
            }

            .step-input-row {
                display: flex;
                align-items: center;
                gap: 12px;
                margin-bottom: 12px;
                flex: 1;
            }

            .step-input-label {
                font-size: 14px;
                min-width: 80px;
                gap: 6px;
            }

            .step-input-label::before {
                font-size: 16px;
            }

            .step-input {
                padding: 8px 10px;
                border-radius: 8px;
                font-size: 13px;
                flex: 1;
                text-align: left;
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
            }

            .step-input:focus {
                box-shadow: 0 0 0 3px rgba(7, 193, 96, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
                transform: scale(1.02);
            }

            .execute-step-btn {
                width: 100%;
                margin: 0;
                justify-self: center;
            }
        }

        .execute-step-btn {
            background: linear-gradient(135deg, #07c160 0%, #06ad56 100%);
            color: white;
            border: none;
            padding: 14px 24px;
            border-radius: 12px;
            font-size: 15px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 16px rgba(7, 193, 96, 0.25);
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
            min-width: 120px;
            justify-content: center;
        }

        .execute-step-btn::before {
            content: "▶️";
            font-size: 14px;
            filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2));
        }

        .execute-step-btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s;
        }

        .execute-step-btn:hover {
            background: linear-gradient(135deg, #06ad56 0%, #059649 100%);
            transform: translateY(-3px);
            box-shadow: 0 8px 24px rgba(7, 193, 96, 0.4);
        }

        .execute-step-btn:hover::after {
            left: 100%;
        }

        .execute-step-btn:active {
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(7, 193, 96, 0.3);
        }

        /* 移动端执行按钮适配 */
        @media (max-width: 768px) {
            .execute-step-btn {
                padding: 14px 24px;
                border-radius: 25px;
                font-size: 16px;
                font-weight: 600;
                box-shadow: 0 4px 16px rgba(7, 193, 96, 0.25);
                min-width: 120px;
                gap: 8px;
                background: linear-gradient(135deg, #07c160, #06ad56);
                border: none;
                color: white;
                letter-spacing: 0.5px;
            }

            .execute-step-btn::before {
                font-size: 14px;
            }

            .execute-step-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(7, 193, 96, 0.35);
                background: linear-gradient(135deg, #06ad56, #059c4f);
            }

            .execute-step-btn:active {
                transform: translateY(0);
                box-shadow: 0 2px 8px rgba(7, 193, 96, 0.3);
            }
        }

        .manage-accounts-btn {
            background: #07c160;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 12px 25px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
        }

        .manage-accounts-btn:hover {
            background: #06ad56;
        }

        /* 验证结果模态框样式 */
        .verification-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0,0,0,0.4), rgba(0,0,0,0.6));
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 10000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
            box-sizing: border-box;
        }

        .verification-container {
            width: 100%;
            max-width: 420px;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 24px;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.15),
                0 8px 25px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            padding: 40px 30px 30px;
            text-align: center;
            position: relative;
            animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-40px) scale(0.9);
                filter: blur(4px);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0);
            }
        }

        .verification-icon {
            font-size: 72px;
            margin-bottom: 24px;
            display: inline-block;
            position: relative;
        }

        .verification-icon.success {
            color: #07c160;
            text-shadow: 0 2px 8px rgba(7, 193, 96, 0.3);
            animation: successPulse 0.6s ease-out;
        }

        .verification-icon.error {
            color: #ff4757;
            text-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
            animation: errorShake 0.6s ease-out;
        }

        .verification-icon.loading {
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: loadingSpin 1.2s linear infinite;
        }

        @keyframes successPulse {
            0% { transform: scale(0.8); opacity: 0.5; }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); opacity: 1; }
        }

        @keyframes errorShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-8px); }
            75% { transform: translateX(8px); }
        }

        @keyframes loadingSpin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .verification-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 16px;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: -0.5px;
        }

        .verification-message {
            font-size: 16px;
            color: #5a6c7d;
            line-height: 1.6;
            margin-bottom: 28px;
            font-weight: 400;
        }

        .verification-account {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #e3e8ed;
            border-radius: 16px;
            padding: 16px 20px;
            margin: 20px 0;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 15px;
            font-weight: 500;
            color: #2c3e50;
            word-break: break-all;
            position: relative;
            box-shadow:
                inset 0 1px 3px rgba(0, 0, 0, 0.1),
                0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .verification-account::before {
            content: '📧';
            position: absolute;
            left: -8px;
            top: -8px;
            background: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .verification-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-top: 8px;
        }

        .verification-btn {
            padding: 14px 28px;
            border: none;
            border-radius: 28px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 110px;
            position: relative;
            overflow: hidden;
        }

        .verification-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .verification-btn:hover::before {
            left: 100%;
        }

        .verification-btn-primary {
            background: linear-gradient(135deg, #07c160, #06ad56);
            color: white;
            box-shadow:
                0 4px 15px rgba(7, 193, 96, 0.3),
                0 2px 8px rgba(7, 193, 96, 0.2);
        }

        .verification-btn-primary:hover {
            background: linear-gradient(135deg, #06ad56, #059649);
            transform: translateY(-2px);
            box-shadow:
                0 6px 20px rgba(7, 193, 96, 0.4),
                0 4px 12px rgba(7, 193, 96, 0.3);
        }

        .verification-btn-primary:active {
            transform: translateY(0);
        }

        .verification-btn-secondary {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #495057;
            border: 2px solid #dee2e6;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .verification-btn-secondary:hover {
            background: linear-gradient(135deg, #e9ecef, #dee2e6);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .verification-close {
            position: absolute;
            top: 16px;
            right: 16px;
            cursor: pointer;
            color: #6c757d;
            font-size: 18px;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 1px solid #dee2e6;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .verification-close:hover {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            transform: rotate(90deg) scale(1.1);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }

        .verification-close:active {
            transform: rotate(90deg) scale(0.95);
        }

        /* 移除顶部彩色装饰条 */

        /* 响应式优化 */
        @media (max-width: 480px) {
            .verification-container {
                margin: 10px;
                padding: 35px 25px 25px;
            }

            .verification-icon {
                font-size: 64px;
                margin-bottom: 20px;
            }

            .verification-title {
                font-size: 22px;
            }

            .verification-message {
                font-size: 15px;
            }

            .verification-btn {
                padding: 12px 24px;
                font-size: 15px;
                min-width: 100px;
            }

            .verification-buttons {
                gap: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 首页 -->
        <div class="page active" id="home">
            <div class="page-title">微信小程序跳转工具</div>

            <!-- 账号管理区域 -->
            <div class="info-card">
                <strong>运动账号管理</strong>
            </div>

            <!-- 账号状态显示区域 -->
            <div id="accountStatusArea">
                <!-- 这里将通过JavaScript动态显示账号状态 -->
            </div>

            <div class="info-card" style="margin-top: 20px;">
                <strong>当前配置：</strong> AppID: <?php echo APPID; ?><br>
                支持明文URL Scheme、加密URL Scheme、加密URL Link三种跳转方式
            </div>

            <form id="linkForm">
                <div class="form-item">
                    <label for="type">跳转类型</label>
                    <select name="type" id="type">
                        <option value="plain">明文URL Scheme（推荐）</option>
                        <option value="encrypted_scheme">加密URL Scheme</option>
                        <option value="encrypted_link">加密URL Link</option>
                        <option value="all">生成所有类型</option>
                    </select>
                </div>

                <div class="form-item">
                    <label for="path">小程序页面路径</label>
                    <input type="text" name="path" id="path" placeholder="例如：pages/index/index">
                    <small>留空则跳转到小程序首页</small>
                </div>

                <div class="form-item">
                    <label for="query">查询参数</label>
                    <textarea name="query" id="query" placeholder="例如：id=123&name=test"></textarea>
                    <small>格式：key1=value1&key2=value2</small>
                </div>

                <div class="form-item">
                    <label for="env_version">版本类型</label>
                    <select name="env_version" id="env_version">
                        <option value="release">正式版</option>
                        <option value="trial">体验版</option>
                        <option value="develop">开发版</option>
                    </select>
                </div>

                <button type="submit" class="btn-primary" id="generateBtn">
                    <span id="btnText">生成跳转链接</span>
                </button>
            </form>

            <!-- 加载状态 -->
            <div class="loading" id="loading">
                <div class="loading-spinner"></div>
                <p>正在生成链接，请稍候...</p>
            </div>

            <!-- 结果显示 -->
            <div id="results"></div>
        </div>

        <!-- 工具页 -->
        <div class="page" id="tools">
            <div class="page-title">工具箱</div>

            <div style="padding: 20px; text-align: center; color: #666;">
                <p style="margin-bottom: 20px;">更多工具功能正在开发中...</p>

                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-top: 30px;">
                    <a href="demo.html" style="display: block; padding: 20px; background: #f8f9fa; border-radius: 8px; text-decoration: none; color: #333;">
                        <div style="font-size: 24px; margin-bottom: 8px;">🎯</div>
                        <div style="font-size: 14px;">使用演示</div>
                    </a>
                    <a href="api.php" style="display: block; padding: 20px; background: #f8f9fa; border-radius: 8px; text-decoration: none; color: #333;">
                        <div style="font-size: 24px; margin-bottom: 8px;">🔗</div>
                        <div style="font-size: 14px;">API接口</div>
                    </a>
                </div>
            </div>
        </div>

        <!-- 我的页面 -->
        <div class="page" id="profile">
            <div class="page-title">关于</div>

            <div class="about-content">
                <h3>项目介绍</h3>
                <p>这是一个基于微信官方URL Scheme和URL Link方案开发的专业工具，支持三种不同的小程序跳转方式，为开发者提供便捷的H5到小程序跳转解决方案。</p>

                <h3>功能特点</h3>
                <ul class="feature-list">
                    <li>明文URL Scheme - 无需接口调用，直接拼接生成</li>
                    <li>加密URL Scheme - 通过微信接口生成，更加安全</li>
                    <li>加密URL Link - 支持微信外打开，适合分享传播</li>
                    <li>智能缓存机制 - 自动缓存access_token，提高效率</li>
                    <li>完善错误处理 - 详细的错误提示和日志记录</li>
                </ul>

                <h3>技术栈</h3>
                <p>PHP + HTML + CSS + JavaScript</p>
                <p>版本：v1.0.0</p>
            </div>

            <!-- 开发者信息 -->
            <div class="developer-info">
                <img src="img/tx.jpg" alt="开发者头像" class="developer-avatar">
                <div class="developer-name">你的名字</div>
                <div class="developer-desc">全栈开发工程师</div>
                <div class="developer-links">
                    <a href="mailto:<EMAIL>">邮箱</a>
                    <a href="https://your-website.com">网站</a>
                    <a href="https://github.com/your-username">GitHub</a>
                </div>
            </div>
        </div>

        <!-- 账号管理弹窗 -->
        <div class="account-switch-modal" id="accountSwitchModal">
            <div class="account-switch-container">
                <div class="account-switch-header">
                    <h3 class="account-switch-title">切换账号</h3>
                    <p class="account-switch-subtitle">选择已保存的账号或添加新账号</p>
                </div>

                <div class="account-list-wrapper">
                    <div class="account-list" id="accountList">
                        <!-- 账号列表将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 添加账号表单，默认隐藏 -->
                <div id="addAccountForm" class="add-account-form" style="display: none;">
                    <div class="add-account-header">
                        <h4 class="add-account-title">添加新账号</h4>
                    </div>
                    <input type="text" id="newAccountInput" placeholder="请输入账号" class="add-account-input">
                    <input type="password" id="newPasswordInput" placeholder="请输入密码" class="add-account-input">
                    <div class="add-account-buttons">
                        <button id="clearInputBtn" class="add-account-btn add-account-btn-clear" style="display: none;">清空输入</button>
                        <button id="cancelAddBtn" class="add-account-btn add-account-btn-cancel">取消</button>
                        <button id="confirmAddBtn" class="add-account-btn add-account-btn-save">保存</button>
                    </div>
                </div>

                <div class="account-switch-footer" id="addAccountBtnContainer">
                    <button id="addNewAccountBtn" class="add-new-account-btn">
                        ➕ 添加新账号
                    </button>
                </div>

                <div class="account-switch-close" id="closeAccountModal">
                    ✕
                </div>
            </div>
        </div>

        <!-- 验证结果模态框 -->
        <div class="verification-modal" id="verificationModal">
            <div class="verification-container">
                <div class="verification-close" id="closeVerificationModal">✕</div>

                <div class="verification-icon" id="verificationIcon">⏳</div>

                <div class="verification-title" id="verificationTitle">验证中</div>

                <div class="verification-message" id="verificationMessage">
                    正在验证账号信息，请稍候...
                </div>

                <div class="verification-account" id="verificationAccount" style="display: none;">
                    <!-- 账号信息将在这里显示 -->
                </div>

                <div class="verification-buttons" id="verificationButtons" style="display: none;">
                    <button class="verification-btn verification-btn-secondary" id="verificationCancelBtn">取消</button>
                    <button class="verification-btn verification-btn-primary" id="verificationConfirmBtn">确定</button>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="tabbar">
            <a href="#" class="tabbar-item active" data-page="home">
                <img src="img/home.png" alt="首页" class="icon-normal" style="display: none;">
                <img src="img/home-1.png" alt="首页" class="icon-active" style="display: block;">
                <span>首页</span>
            </a>
            <a href="#" class="tabbar-item" data-page="tools">
                <img src="img/gd.png" alt="工具" class="icon-normal">
                <img src="img/gd-1.png" alt="工具" class="icon-active" style="display: none;">
                <span>工具</span>
            </a>
            <a href="#" class="tabbar-item" data-page="profile">
                <img src="img/user.png" alt="我的" class="icon-normal">
                <img src="img/user-1.png" alt="我的" class="icon-active" style="display: none;">
                <span>我的</span>
            </a>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="alertModal" class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <span id="modalIcon" class="modal-icon">🔥</span>
                <h3 id="modalTitle" class="modal-title">操作提示</h3>
                <p id="modalMessage" class="modal-message">正在执行操作...</p>
            </div>
            <div class="modal-actions">
                <button id="modalConfirmBtn" class="modal-btn modal-btn-primary" onclick="closeModal()">确定</button>
            </div>
        </div>
    </div>

    <script>
        // 账号管理器
        window.AccountManager = {
            // 获取保存的账号列表
            getAccounts: function() {
                try {
                    const accounts = localStorage.getItem('savedAccounts');
                    return accounts ? JSON.parse(accounts) : [];
                } catch (error) {
                    console.error('获取账号失败:', error);
                    return [];
                }
            },

            // 保存账号列表
            saveAccounts: function(accounts) {
                try {
                    localStorage.setItem('savedAccounts', JSON.stringify(accounts));
                    return true;
                } catch (error) {
                    console.error('保存账号失败:', error);
                    return false;
                }
            },

            // 添加账号
            addAccount: function(account, password) {
                if (!account || !password) {
                    return { success: false, error: 'invalid_input' };
                }

                const accounts = this.getAccounts();

                // 检查是否已存在相同账号
                const existingIndex = accounts.findIndex(acc => acc.account === account);

                if (existingIndex !== -1) {
                    // 账号已存在，拒绝添加
                    return { success: false, error: 'duplicate_account' };
                } else {
                    // 添加新账号
                    accounts.push({
                        account: account,
                        password: password,
                        addTime: new Date().getTime()
                    });
                }

                const saveResult = this.saveAccounts(accounts);
                return { success: saveResult, error: saveResult ? null : 'save_failed' };
            },

            // 删除账号
            deleteAccount: function(account) {
                if (!account) return false;

                let accounts = this.getAccounts();
                accounts = accounts.filter(acc => acc.account !== account);

                return this.saveAccounts(accounts);
            },

            // 清空所有账号
            clearAccounts: function() {
                return this.saveAccounts([]);
            }
        };

        // 底部导航切换
        document.querySelectorAll('.tabbar-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();

                // 移除所有active状态
                document.querySelectorAll('.tabbar-item').forEach(tab => {
                    tab.classList.remove('active');
                    const normalIcon = tab.querySelector('.icon-normal');
                    const activeIcon = tab.querySelector('.icon-active');
                    if (normalIcon) normalIcon.style.display = 'block';
                    if (activeIcon) activeIcon.style.display = 'none';
                });

                document.querySelectorAll('.page').forEach(page => {
                    page.classList.remove('active');
                });

                // 激活当前选中的
                this.classList.add('active');
                const normalIcon = this.querySelector('.icon-normal');
                const activeIcon = this.querySelector('.icon-active');
                if (normalIcon) normalIcon.style.display = 'none';
                if (activeIcon) {
                    activeIcon.style.display = 'block';
                } else {
                    // 如果没有选中状态图标，保持普通图标显示
                    if (normalIcon) normalIcon.style.display = 'block';
                }

                const targetPage = this.getAttribute('data-page');
                document.getElementById(targetPage).classList.add('active');
            });
        });

        // 账号管理界面功能
        const accountStatusArea = document.getElementById('accountStatusArea');
        const accountSwitchModal = document.getElementById('accountSwitchModal');
        const accountList = document.getElementById('accountList');
        const addNewAccountBtn = document.getElementById('addNewAccountBtn');
        const addAccountForm = document.getElementById('addAccountForm');
        const newAccountInput = document.getElementById('newAccountInput');
        const newPasswordInput = document.getElementById('newPasswordInput');
        const confirmAddBtn = document.getElementById('confirmAddBtn');
        const cancelAddBtn = document.getElementById('cancelAddBtn');
        const clearInputBtn = document.getElementById('clearInputBtn');
        const closeAccountModal = document.getElementById('closeAccountModal');
        const addAccountBtnContainer = document.getElementById('addAccountBtnContainer');

        // 渲染账号状态区域
        function renderAccountStatus() {
            const accounts = window.AccountManager.getAccounts();

            if (accounts.length === 0) {
                // 没有账号时显示添加提示
                accountStatusArea.innerHTML = `
                    <div class="account-status-container">
                        <div class="no-accounts-icon">📱</div>
                        <div class="no-accounts-message">
                            暂无运动账号<br>
                            <small style="color: #999; font-size: 14px;">添加小米运动账号开始使用</small>
                        </div>
                        <button class="add-first-account-btn" onclick="showAccountSwitchModal()">
                            ➕ 添加账号
                        </button>
                    </div>
                `;
            } else {
                // 有账号时显示账号列表
                let accountsHtml = `
                    <div class="accounts-list-container">
                        <div class="accounts-list-title">已保存的运动账号 (${accounts.length})</div>
                `;

                // 只显示第一个账号
                const firstAccount = accounts[0];
                accountsHtml += `
                    <div class="account-summary-item">
                        <div class="account-info-section">
                            <span class="account-summary-text">${firstAccount.account}</span>
                            <button class="switch-account-btn" onclick="switchToAccount('${firstAccount.account}', '${firstAccount.password}')">
                                切换账号
                            </button>
                        </div>
                        <div class="step-input-container">
                            <div class="step-input-row">
                                <label for="stepInput" class="step-input-label">热量值</label>
                                <input type="number" id="stepInput" class="step-input" value="" min="1" max="10" placeholder="请输入热量值 (1-10)">
                            </div>
                            <button class="execute-step-btn" onclick="executeStep('${firstAccount.account}', '${firstAccount.password}')">
                                执行操作
                            </button>
                        </div>
                    </div>
                `;

                accountsHtml += `
                    </div>
                `;

                accountStatusArea.innerHTML = accountsHtml;
            }
        }

        // 页面加载时渲染账号状态
        renderAccountStatus();

        // 验证模态框功能
        const verificationModal = document.getElementById('verificationModal');
        const verificationIcon = document.getElementById('verificationIcon');
        const verificationTitle = document.getElementById('verificationTitle');
        const verificationMessage = document.getElementById('verificationMessage');
        const verificationAccount = document.getElementById('verificationAccount');
        const verificationButtons = document.getElementById('verificationButtons');
        const verificationConfirmBtn = document.getElementById('verificationConfirmBtn');
        const verificationCancelBtn = document.getElementById('verificationCancelBtn');
        const closeVerificationModal = document.getElementById('closeVerificationModal');

        // 显示验证模态框
        function showVerificationModal(type, title, message, account = null) {
            verificationModal.style.display = 'flex';
            document.body.style.overflow = 'hidden';

            // 设置图标
            if (type === 'loading') {
                verificationIcon.textContent = '⏳';
                verificationIcon.className = 'verification-icon loading';
            } else if (type === 'success') {
                verificationIcon.textContent = '✅';
                verificationIcon.className = 'verification-icon success';
            } else if (type === 'error') {
                verificationIcon.textContent = '❌';
                verificationIcon.className = 'verification-icon error';
            }

            // 设置标题和消息
            verificationTitle.textContent = title;
            verificationMessage.textContent = message;

            // 显示账号信息
            if (account) {
                verificationAccount.textContent = account;
                verificationAccount.style.display = 'block';
            } else {
                verificationAccount.style.display = 'none';
            }

            // 根据类型显示按钮
            if (type === 'loading') {
                verificationButtons.style.display = 'none';
            } else {
                verificationButtons.style.display = 'flex';
            }
        }

        // 关闭验证模态框
        function closeVerificationModalFunc() {
            verificationModal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // 事件监听器
        closeVerificationModal.addEventListener('click', closeVerificationModalFunc);
        verificationCancelBtn.addEventListener('click', closeVerificationModalFunc);
        verificationConfirmBtn.addEventListener('click', closeVerificationModalFunc);

        // 移除点击模态框外部关闭功能，只允许通过X按钮和取消按钮关闭
        verificationModal.addEventListener('click', function(e) {
            // 阻止事件冒泡，防止意外关闭
            e.stopPropagation();
        });

        // 显示账号切换弹窗
        function showAccountSwitchModal() {
            renderAccountList();
            accountSwitchModal.style.display = 'flex';
            addAccountForm.style.display = 'none';
            addNewAccountBtn.style.display = 'block';
            addAccountBtnContainer.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        // 关闭账号切换弹窗
        function closeAccountSwitchModal() {
            accountSwitchModal.style.display = 'none';
            document.body.style.overflow = 'auto';
            // 清空添加账号表单
            newAccountInput.value = '';
            newPasswordInput.value = '';
            // 重新渲染账号状态
            renderAccountStatus();
        }

        // 显示添加账号表单
        function showAddAccountForm() {
            addNewAccountBtn.style.display = 'none';
            addAccountForm.style.display = 'block';
            addAccountBtnContainer.style.display = 'none';
            // 隐藏清空按钮（正常添加新账号时不需要）
            clearInputBtn.style.display = 'none';
        }

        // 隐藏添加账号表单
        function hideAddAccountForm() {
            addAccountForm.style.display = 'none';
            addNewAccountBtn.style.display = 'block';
            addAccountBtnContainer.style.display = 'block';
            // 清空表单
            newAccountInput.value = '';
            newPasswordInput.value = '';
            // 隐藏清空按钮
            clearInputBtn.style.display = 'none';
        }

        // 渲染账号列表
        function renderAccountList() {
            const accounts = window.AccountManager.getAccounts();

            accountList.innerHTML = '';

            if (accounts.length === 0) {
                accountList.innerHTML = '<div class="empty-accounts">暂无已保存的账号</div>';
                addAccountBtnContainer.style.borderTop = '1px solid #f2f2f2';
                return;
            }

            addAccountBtnContainer.style.borderTop = '1px solid #f2f2f2';

            accounts.forEach((acc, index) => {
                const accountItem = document.createElement('div');
                accountItem.className = 'account-item';

                const accountInfo = document.createElement('div');
                accountInfo.className = 'account-info';

                const textContainer = document.createElement('div');
                textContainer.className = 'account-text-container';

                const accountText = document.createElement('div');
                accountText.className = 'account-text';
                accountText.textContent = acc.account;

                const statusText = document.createElement('div');
                statusText.className = 'switch-text';
                statusText.textContent = '已保存';
                statusText.style.backgroundColor = 'rgba(7, 193, 96, 0.05)';
                statusText.style.borderColor = 'rgba(7, 193, 96, 0.2)';
                statusText.style.color = '#07c160';

                textContainer.appendChild(accountText);
                textContainer.appendChild(statusText);
                accountInfo.appendChild(textContainer);

                // 删除按钮
                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'delete-account-btn';
                deleteBtn.innerHTML = '🗑️';
                deleteBtn.title = '删除账号';

                // 添加点击切换功能
                accountInfo.style.cursor = 'pointer';
                accountInfo.addEventListener('click', function() {
                    // 直接切换到该账号
                    switchToSelectedAccount(acc.account, acc.password);
                });

                // 点击删除按钮事件
                deleteBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    if (confirm('确定要删除账号 ' + acc.account + ' 吗？')) {
                        window.AccountManager.deleteAccount(acc.account);
                        renderAccountList();
                    }
                });

                accountItem.appendChild(accountInfo);
                accountItem.appendChild(deleteBtn);
                accountList.appendChild(accountItem);
            });
        }

        // 事件监听器
        addNewAccountBtn.addEventListener('click', showAddAccountForm);
        cancelAddBtn.addEventListener('click', hideAddAccountForm);
        closeAccountModal.addEventListener('click', closeAccountSwitchModal);

        // 清空输入按钮事件
        clearInputBtn.addEventListener('click', function() {
            newAccountInput.value = '';
            newPasswordInput.value = '';
            clearInputBtn.style.display = 'none';
            newAccountInput.focus();
        });

        // 移除点击弹窗外部关闭功能，只允许通过X按钮和取消按钮关闭
        accountSwitchModal.addEventListener('click', function(e) {
            // 阻止事件冒泡，防止意外关闭
            e.stopPropagation();
        });

        // 确认添加账号
        confirmAddBtn.addEventListener('click', async function() {
            const account = newAccountInput.value.trim();
            const password = newPasswordInput.value;

            if (!account) {
                // 隐藏添加账号弹窗
                accountSwitchModal.style.display = 'none';
                // 显示错误模态框（不显示账号信息）
                showVerificationModal('error', '输入错误', '请输入账号信息');
                // 设置按钮事件
                verificationConfirmBtn.onclick = function() {
                    closeVerificationModalFunc();
                    accountSwitchModal.style.display = 'flex';
                };
                verificationCancelBtn.onclick = function() {
                    closeVerificationModalFunc();
                    accountSwitchModal.style.display = 'flex';
                };
                return;
            }

            if (!password) {
                // 隐藏添加账号弹窗
                accountSwitchModal.style.display = 'none';
                // 显示错误模态框（不显示账号信息）
                showVerificationModal('error', '输入错误', '请输入密码信息');
                // 设置按钮事件
                verificationConfirmBtn.onclick = function() {
                    closeVerificationModalFunc();
                    accountSwitchModal.style.display = 'flex';
                };
                verificationCancelBtn.onclick = function() {
                    closeVerificationModalFunc();
                    accountSwitchModal.style.display = 'flex';
                };
                return;
            }

            // 验证账号格式
            const emailOrPhoneRegex = /^(?:[^\s@]+@[^\s@]+\.[^\s@]+$|1[3-9]\d{9})$/;
            if (!emailOrPhoneRegex.test(account)) {
                // 隐藏添加账号弹窗
                accountSwitchModal.style.display = 'none';
                // 显示错误模态框（不显示账号信息）
                showVerificationModal('error', '格式错误', '请输入有效的邮箱地址或手机号');
                // 设置按钮事件
                verificationConfirmBtn.onclick = function() {
                    closeVerificationModalFunc();
                    accountSwitchModal.style.display = 'flex';
                };
                verificationCancelBtn.onclick = function() {
                    closeVerificationModalFunc();
                    accountSwitchModal.style.display = 'flex';
                };
                return;
            }

            // 检查账号是否已存在
            const existingAccounts = window.AccountManager.getAccounts();
            const isDuplicate = existingAccounts.some(acc => acc.account === account);
            if (isDuplicate) {
                // 隐藏添加账号弹窗
                accountSwitchModal.style.display = 'none';
                // 显示重复账号错误
                showVerificationModal('error', '账号已存在', '该账号已经添加过了，请勿重复添加');
                // 设置按钮事件
                verificationConfirmBtn.onclick = function() {
                    closeVerificationModalFunc();
                    accountSwitchModal.style.display = 'flex';
                };
                verificationCancelBtn.onclick = function() {
                    closeVerificationModalFunc();
                    accountSwitchModal.style.display = 'flex';
                };
                return;
            }

            // 显示验证中的模态框
            showVerificationModal('loading', '验证账号', '正在验证账号信息，请稍候...', account);

            // 隐藏添加账号弹窗
            accountSwitchModal.style.display = 'none';

            try {
                // 先验证账号
                const validation = await validateAccount(account, password);

                if (validation.success) {
                    // 验证成功，直接保存账号
                    const addResult = window.AccountManager.addAccount(account, password);

                    if (addResult.success) {
                        // 保存成功
                        closeVerificationModalFunc();
                        renderAccountList();
                        hideAddAccountForm();
                        renderAccountStatus();

                        // 显示简短的成功提示
                        showVerificationModal('success', '添加成功', '账号已成功添加', account);

                        // 设置按钮事件 - 只需要确定按钮
                        verificationConfirmBtn.onclick = function() {
                            closeVerificationModalFunc();
                        };
                        verificationCancelBtn.onclick = function() {
                            closeVerificationModalFunc();
                        };
                    } else {
                        // 保存失败，根据错误类型显示不同提示
                        let errorTitle = '保存失败';
                        let errorMessage = '账号保存失败，请重试';

                        if (addResult.error === 'duplicate_account') {
                            errorTitle = '账号已存在';
                            errorMessage = '该账号已经添加过了，请勿重复添加';
                        } else if (addResult.error === 'invalid_input') {
                            errorTitle = '输入错误';
                            errorMessage = '账号或密码信息无效';
                        }

                        showVerificationModal('error', errorTitle, errorMessage);
                        // 设置按钮事件
                        verificationConfirmBtn.onclick = function() {
                            closeVerificationModalFunc();
                            accountSwitchModal.style.display = 'flex';
                        };
                        verificationCancelBtn.onclick = function() {
                            closeVerificationModalFunc();
                            accountSwitchModal.style.display = 'flex';
                        };
                    }

                } else {
                    // 验证失败（不显示账号信息）
                    showVerificationModal('error', '验证失败', validation.message);

                    // 设置按钮事件
                    verificationConfirmBtn.onclick = function() {
                        closeVerificationModalFunc();
                        // 重新显示添加账号弹窗
                        accountSwitchModal.style.display = 'flex';
                    };

                    verificationCancelBtn.onclick = function() {
                        closeVerificationModalFunc();
                        // 重新显示添加账号弹窗
                        accountSwitchModal.style.display = 'flex';
                    };
                }
            } catch (error) {
                showVerificationModal('error', '验证错误', '验证过程中出现网络错误，请检查网络连接后重试');

                // 设置按钮事件
                verificationConfirmBtn.onclick = function() {
                    closeVerificationModalFunc();
                    accountSwitchModal.style.display = 'flex';
                };

                verificationCancelBtn.onclick = function() {
                    closeVerificationModalFunc();
                    accountSwitchModal.style.display = 'flex';
                };
            }
        });

        // 账号验证功能（在添加账号时调用）
        async function validateAccount(username, password) {
            try {
                // 按照指定格式构建请求URL
                const apiUrl = `mi.php?user=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}&step=1`;

                const response = await fetch(apiUrl, {
                    method: 'GET'
                });

                const data = await response.json();

                // 根据返回的code判断结果
                if (data.code === 200) {
                    return { success: true, message: '账号验证成功' };
                } else if (data.code === 201) {
                    return { success: false, message: '用户名或密码错误' };
                } else if (data.code === -1) {
                    return { success: false, message: data.msg || '参数错误' };
                } else {
                    return { success: false, message: data.msg || '验证失败' };
                }
            } catch (error) {
                return { success: false, message: '网络错误，请检查连接后重试' };
            }
        }

        // 表单提交处理
        document.getElementById('linkForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            formData.append('action', 'generate');

            const generateBtn = document.getElementById('generateBtn');
            const btnText = document.getElementById('btnText');
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');

            // 显示加载状态
            generateBtn.disabled = true;
            btnText.textContent = '生成中...';
            loading.style.display = 'block';
            results.innerHTML = '';

            try {
                const response = await fetch('', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    displayResults(data.data);
                } else {
                    showAlert('error', data.message || '生成失败，请重试', '生成失败');
                }
            } catch (error) {
                showAlert('error', '网络错误，请检查连接后重试', '网络错误');
            } finally {
                // 恢复按钮状态
                generateBtn.disabled = false;
                btnText.textContent = '生成跳转链接';
                loading.style.display = 'none';
            }
        });

        // 显示结果
        function displayResults(data) {
            const results = document.getElementById('results');
            let html = '';

            const typeNames = {
                'plain_scheme': { name: '明文URL Scheme', desc: '推荐使用' },
                'encrypted_scheme': { name: '加密URL Scheme', desc: '安全可靠' },
                'encrypted_link': { name: '加密URL Link', desc: '支持微信外打开' }
            };

            Object.keys(data).forEach(key => {
                const type = typeNames[key];
                if (type) {
                    html += `
                        <div class="result-card">
                            <div class="result-title">${type.name} (${type.desc})</div>
                            <div class="result-url" id="${key}">${data[key]}</div>
                            <div class="result-actions">
                                <button class="btn-small btn-copy" onclick="copyToClipboard('${key}')">复制链接</button>
                                <button class="btn-small btn-test" onclick="testLink('${data[key]}')">测试打开</button>
                            </div>
                        </div>
                    `;
                }
            });

            results.innerHTML = html;
        }

        // 复制到剪贴板
        async function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;

            try {
                await navigator.clipboard.writeText(text);
                showAlert('success', '链接已复制到剪贴板', '复制成功');
            } catch (err) {
                // 兼容旧浏览器
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showAlert('success', '链接已复制到剪贴板', '复制成功');
            }
        }

        // 测试链接
        function testLink(url) {
            window.open(url, '_blank');
        }

        // 显示模态框提示信息
        function showAlert(type, message, title = '操作提示') {
            const modal = document.getElementById('alertModal');
            const modalIcon = document.getElementById('modalIcon');
            const modalTitle = document.getElementById('modalTitle');
            const modalMessage = document.getElementById('modalMessage');
            const modalContent = modal.querySelector('.modal-content');

            // 设置图标和样式
            if (type === 'success') {
                modalIcon.textContent = '✅';
                modalTitle.textContent = title || '操作成功';
                modalContent.style.borderTop = '4px solid #07c160';
            } else if (type === 'error') {
                modalIcon.textContent = '❌';
                modalTitle.textContent = title || '操作失败';
                modalContent.style.borderTop = '4px solid #dc3545';
            } else if (type === 'info') {
                modalIcon.textContent = 'ℹ️';
                modalTitle.textContent = title || '提示信息';
                modalContent.style.borderTop = '4px solid #17a2b8';
            } else {
                modalIcon.textContent = '🔥';
                modalTitle.textContent = title || '操作提示';
                modalContent.style.borderTop = '4px solid #07c160';
            }

            modalMessage.textContent = message;

            // 显示模态框
            modal.classList.add('show');

            // 3秒后自动关闭（成功类型）
            if (type === 'success') {
                setTimeout(() => {
                    closeModal();
                }, 3000);
            }
        }

        // 关闭模态框
        function closeModal() {
            const modal = document.getElementById('alertModal');
            modal.classList.remove('show');
        }

        // 点击遮罩层关闭模态框
        document.getElementById('alertModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });

        // 切换到指定账号（从主页面的切换账号按钮调用）
        function switchToAccount(account, password) {
            // 显示账号切换弹窗，显示账号列表供用户选择
            showAccountSwitchModal();
        }

        // 切换到选中的账号（从账号列表点击调用）
        function switchToSelectedAccount(account, password) {
            // 重新排列账号顺序，将选中的账号移到第一位
            const accounts = window.AccountManager.getAccounts();
            const selectedIndex = accounts.findIndex(acc => acc.account === account);

            if (selectedIndex > 0) {
                // 将选中的账号移到第一位
                const selectedAccount = accounts.splice(selectedIndex, 1)[0];
                accounts.unshift(selectedAccount);

                // 保存新的账号顺序
                window.AccountManager.saveAccounts(accounts);
            }

            // 关闭切换弹窗
            closeAccountSwitchModal();

            // 重新渲染主页面账号状态
            renderAccountStatus();

            // 显示切换成功提示
            showAlert('success', `已切换到账号: ${account}`, '切换成功');
        }

        // 执行step操作
        function executeStep(account, password) {
            const stepInput = document.getElementById('stepInput');
            const step = stepInput ? stepInput.value : '';

            if (!step || step === '' || isNaN(step) || step < 1 || step > 10) {
                showAlert('error', '请输入有效的热量值 (1-10)', '参数错误');
                return;
            }

            // 显示任务信息
            showTaskInfo(account, step, password);

            // 显示成功提示
            showAlert('success', `任务已生成，请按照下方提示完成操作`, '任务生成成功');
        }

        // 显示任务信息
        function showTaskInfo(account, step, password) {
            const accountStatusArea = document.getElementById('accountStatusArea');

            if (!accountStatusArea) {
                console.error('找不到accountStatusArea元素！');
                return;
            }

            // 查找现有的任务信息卡片并移除
            const existingTaskCard = accountStatusArea.querySelector('.task-info-card');
            if (existingTaskCard) {
                existingTaskCard.remove();
            }

            // 创建任务信息卡片
            const taskInfoHtml = `
                <div class="task-info-card">
                    <div class="task-info-header">
                        <span class="task-info-icon">📋</span>
                        <h3 class="task-info-title">任务内容</h3>
                    </div>
                    <div class="task-info-content">
                        <div class="task-info-item">
                            <span class="task-info-label">广告状态:</span>
                            <span class="task-info-value task-info-status">未观看</span>
                        </div>
                        <div class="task-info-item">
                            <span class="task-info-label">广告任务:</span>
                            <span class="task-info-value">跳转到小程序观看一次广告</span>
                        </div>
                        <div class="task-info-tip">
                            <strong>Tips:</strong> 点击下方按钮一键跳转
                        </div>
                    </div>
                    <button class="ad-jump-btn" onclick="jumpToAd('${account}', '${password}', '${step}')">
                        一键跳转观看广告
                    </button>
                </div>
            `;

            // 将任务信息卡片添加到账号状态区域
            accountStatusArea.insertAdjacentHTML('beforeend', taskInfoHtml);

            // 平滑滚动到任务信息卡片
            setTimeout(() => {
                const taskCard = accountStatusArea.querySelector('.task-info-card');
                if (taskCard) {
                    taskCard.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                }
            }, 100);
        }

        // 跳转到广告页面
        function jumpToAd(account, password, step) {
            // 构建请求URL
            const url = `mi.php?user=${encodeURIComponent(account)}&password=${encodeURIComponent(password)}&step=${step}`;

            // 在新窗口中打开
            window.open(url, '_blank');

            // 显示跳转提示
            showAlert('success', '已跳转到广告页面，请在新窗口中完成观看', '跳转成功');

            // 更新任务状态
            updateTaskStatus();
        }

        // 更新任务状态
        function updateTaskStatus() {
            const taskCard = document.querySelector('.task-info-card');
            if (taskCard) {
                const statusElement = taskCard.querySelector('.task-info-status');
                if (statusElement) {
                    statusElement.textContent = '已跳转';
                    statusElement.style.color = '#ff9500';
                }

                const tipElement = taskCard.querySelector('.task-info-tip');
                if (tipElement) {
                    tipElement.innerHTML = '<strong>Tips:</strong> 请在新窗口中完成广告观看，完成后返回查看结果';
                }
            }
        }
    </script>
</body>
</html>

<?php
/**
 * 微信小程序配置文件
 */

// 小程序基本信息
define('WECHAT_APPID', 'wx886d1444ae5839e3');
define('WECHAT_APP_SECRET', 'c3fbeb889f8751edbb6c5ecf4fe5aa50');

// 缓存配置
define('CACHE_DIR', __DIR__ . '/cache');
define('ACCESS_TOKEN_CACHE_FILE', CACHE_DIR . '/access_token.json');
define('ACCESS_TOKEN_EXPIRE_TIME', 7200); // access_token有效期2小时

// 日志配置
define('LOG_DIR', __DIR__ . '/logs');
define('LOG_FILE', LOG_DIR . '/wechat.log');

// 创建必要的目录
if (!is_dir(CACHE_DIR)) {
    mkdir(CACHE_DIR, 0755, true);
}

if (!is_dir(LOG_DIR)) {
    mkdir(LOG_DIR, 0755, true);
}

/**
 * 日志记录函数
 */
function writeLog($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    file_put_contents(LOG_FILE, $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * 缓存access_token
 */
function cacheAccessToken($accessToken, $expiresIn, $isStable = false) {
    $data = [
        'access_token' => $accessToken,
        'expires_at' => time() + $expiresIn - 300, // 提前5分钟过期
        'is_stable' => $isStable,
        'created_at' => time()
    ];
    file_put_contents(ACCESS_TOKEN_CACHE_FILE, json_encode($data), LOCK_EX);
    $tokenType = $isStable ? 'Stable' : 'Regular';
    writeLog("{$tokenType} access token cached, expires at: " . date('Y-m-d H:i:s', $data['expires_at']));
}

/**
 * 获取缓存的access_token
 */
function getCachedAccessToken() {
    if (!file_exists(ACCESS_TOKEN_CACHE_FILE)) {
        return null;
    }

    $data = json_decode(file_get_contents(ACCESS_TOKEN_CACHE_FILE), true);
    if (!$data || !isset($data['access_token']) || !isset($data['expires_at'])) {
        return null;
    }

    if (time() >= $data['expires_at']) {
        $tokenType = isset($data['is_stable']) && $data['is_stable'] ? 'Stable' : 'Regular';
        writeLog("{$tokenType} cached access token expired");
        return null;
    }

    $tokenType = isset($data['is_stable']) && $data['is_stable'] ? 'Stable' : 'Regular';
    writeLog("Using cached {$tokenType} access token");
    return $data['access_token'];
}

/**
 * 获取稳定版access_token
 */
function getStableAccessToken($appid, $appSecret) {
    $url = "https://api.weixin.qq.com/cgi-bin/stable_token";

    $data = [
        'grant_type' => 'client_credential',
        'appid' => $appid,
        'secret' => $appSecret,
        'force_refresh' => false
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen(json_encode($data))
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode !== 200) {
        throw new Exception("获取稳定版access_token失败，HTTP状态码: {$httpCode}");
    }

    $result = json_decode($response, true);

    if (isset($result['access_token'])) {
        // 缓存稳定版access_token
        cacheAccessToken($result['access_token'], $result['expires_in'] ?? 7200, true);
        writeLog("Successfully obtained stable access token");
        return $result['access_token'];
    }

    $errorMsg = '获取稳定版access_token失败: ';
    if (isset($result['errcode'])) {
        $errorMsg .= getErrorMessage($result['errcode']);
    } else {
        $errorMsg .= $response;
    }
    writeLog($errorMsg, 'ERROR');
    throw new Exception($errorMsg);
}

/**
 * 错误码映射
 */
function getErrorMessage($errcode) {
    $errorMessages = [
        // access_token相关错误
        40001 => 'access_token无效或已过期，建议使用稳定版access_token',
        40002 => '请确保grant_type字段值为client_credential',
        40013 => '不合法的AppID，请检查AppID是否正确',
        40125 => '小程序配置无效',
        41002 => '缺少appid参数',
        41004 => '缺少secret参数',
        42001 => 'access_token超时，请重新获取',
        42007 => '用户修改微信密码，access_token失效',

        // 权限相关错误
        43104 => 'appid与openid不匹配',
        45009 => '接口调用超过限制，请稍后重试',
        48001 => 'api功能未授权，请检查小程序权限设置',
        48002 => 'api禁止修改',
        48003 => 'api禁止删除',
        48004 => 'api不可用',
        48005 => 'api禁止分享',

        // 小程序版本相关错误
        85079 => '小程序没有线上版本，请先发布小程序',
        85080 => '小程序没有审核版本',
        85081 => '该版本不能灰度',
        85082 => '小程序没有线上版本',
        85083 => '该版本不能回退',
        85084 => '该版本不能回退',
        85085 => '当前版本不能回退',
        85086 => '当前版本不能回退',
        85087 => '该版本不能设置为体验版',
        85088 => '该版本不能设置为体验版',

        // URL Scheme相关错误
        40165 => '调用接口的IP地址不在白名单中，请在小程序后台配置IP白名单',
        44990 => 'URL Scheme生成失败，请检查参数',
        44991 => 'URL Scheme已过期',
        44992 => 'URL Scheme无效',
        44993 => 'URL Scheme生成次数超过限制',

        // 网络和系统错误
        -1 => '系统繁忙，请稍后重试',
        61023 => '请求过于频繁，请稍后重试',
        61024 => '请求被限制，请稍后重试'
    ];

    return isset($errorMessages[$errcode]) ? $errorMessages[$errcode] : "未知错误码: {$errcode}";
}
?>

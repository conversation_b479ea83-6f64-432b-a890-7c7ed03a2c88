# 微信小程序H5跳转工具

基于微信官方URL Scheme和URL Link方案开发的PHP工具，支持通过H5页面跳转到微信小程序。

## 功能特性

- ✅ **明文URL Scheme**：无需调用接口，直接拼接生成（推荐使用）
- ✅ **加密URL Scheme**：通过微信接口生成，支持自定义参数
- ✅ **加密URL Link**：通过微信接口生成，支持微信外打开
- ✅ **Access Token缓存**：自动缓存和刷新access_token
- ✅ **完善的错误处理**：详细的错误提示和日志记录

## 文件结构

```
├── index.php              # 主页面，Web界面生成跳转链接
├── api.php                # API接口，提供JSON格式调用
├── config.php             # 配置文件，小程序信息和工具函数
├── WechatMiniProgram.php  # 核心类文件，微信小程序跳转功能
├── demo.html              # 演示页面，展示使用场景
├── README.md              # 使用说明文档
├── cache/                 # 缓存目录（自动创建）
└── logs/                  # 日志目录（自动创建）
```

## 快速开始

### 1. 配置小程序信息

编辑 `config.php` 文件，修改小程序AppID和AppSecret：

```php
define('WECHAT_APPID', 'your_appid_here');
define('WECHAT_APP_SECRET', 'your_app_secret_here');
```

### 2. 部署使用

1. 上传所有文件到支持PHP的Web服务器（PHP >= 7.0，需要curl扩展）
2. 确保有文件写入权限（用于缓存和日志）
3. 访问 `index.php` 开始使用

## 使用方法

### Web界面使用

1. 打开 `index.php` 页面
2. 选择跳转类型（推荐明文URL Scheme）
3. 填写页面路径和查询参数（可选）
4. 点击生成链接

### API调用

```bash
# POST请求
curl -X POST http://your-domain.com/api.php \
  -H "Content-Type: application/json" \
  -d '{
    "type": "plain",
    "path": "pages/detail/detail",
    "query": {"id": "123"},
    "env_version": "release"
  }'
```

## 三种跳转方式

- **明文URL Scheme**：无需接口调用，直接拼接，推荐使用
- **加密URL Scheme**：需要调用微信接口生成，更安全
- **加密URL Link**：支持微信外打开，适合分享传播

## 注意事项

1. 明文URL Scheme需要在小程序后台配置允许的页面路径
2. 仅支持非个人主体小程序使用
3. 每个小程序每天总打开次数上限300万次

## 故障排查

如遇问题，请检查：
1. 查看 `logs/wechat.log` 日志文件
2. 确认小程序AppID和AppSecret正确
3. 检查服务器PHP环境和curl扩展
4. 确保有文件读写权限

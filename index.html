
  <!-- 臭逼脑瘫，又来偷你爹代码？偷代码狗出门被大运反复碾压，全家死光 -->

<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>小麦刷步网 - 在线修改微信支付宝步数</title>
  <meta name="description" content="小麦刷步网提供微信、支付宝步数修改服务，助力蚂蚁森林能量收集和微信运动排行榜登顶。一键同步步数，支持自定义步数。">
  <meta name="keywords" content="刷步,微信刷步,支付宝刷步,步数修改,蚂蚁森林能量,微信捐步,小米运动刷步,微信运动步数,手动刷步,自定义步数,免费试用刷步">
  <link rel="stylesheet" href="layui/layui.css" media="all">
  <script src="layui/layui.js"></script>
  <link rel="stylesheet" href="nav.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  
  <!-- 阿里巴巴矢量图标库 -->
  <style>
    /* 项目一：会员图标 */
    @font-face {
      font-family: 'iconfont-member';  /* Project id 4886519 */
      src: url('//at.alicdn.com/t/c/font_4886519_oeu99ksl9a.woff2?t=1744183106446') format('woff2'),
           url('//at.alicdn.com/t/c/font_4886519_oeu99ksl9a.woff?t=1744183106446') format('woff'),
           url('//at.alicdn.com/t/c/font_4886519_oeu99ksl9a.ttf?t=1744183106446') format('truetype');
    }
    
    /* 项目二：客服图标 */
    @font-face {
      font-family: 'iconfont';  /* Project id 4895258 */
      src: url('//at.alicdn.com/t/c/font_4895258_xv7r97cn6w.woff2?t=1750506785618') format('woff2'),
           url('//at.alicdn.com/t/c/font_4895258_xv7r97cn6w.woff?t=1750506785618') format('woff'),
           url('//at.alicdn.com/t/c/font_4895258_xv7r97cn6w.ttf?t=1750506785618') format('truetype');
    }
    
    .iconfont-member {
      font-family: "iconfont-member" !important;
      font-size: 16px;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    .iconfont {
      font-family: "iconfont" !important;
      font-size: 16px;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    /* 客服图标样式 */
    .cs-button .iconfont {
      color: white;
      font-size: 22px;
    }
    
    /* 底部导航图标样式 */
    .nav-icon.member-icon {
      font-family: "iconfont-member" !important;
      font-size: 21px !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      display: flex;
      align-items: center;
      justify-content: center;
      color: currentColor;
      font-weight: bold;
      transform: scale(1.1) translateY(0);
      margin-bottom: 3px;
    }
    
    /* 活动状态下的图标颜色和位置 */
    .nav-item.active .nav-icon.member-icon {
      color: #4db7e9;
      transform: scale(1.1) translateY(-2px);
    }
    
    /* 关闭图标样式 */
    .cs-close span {
      color: #999;
      font-size: 22px;
    }
    
    .cs-close:hover span {
      color: #666;
    }
    
    /* 移除导航栏点击时的灰色高亮效果 */
    .nav-item {
      -webkit-tap-highlight-color: transparent;
      user-select: none;
    }
  </style>
  
  <style>
    body {
      font-family: Arial, \'Microsoft YaHei\', sans-serif;
      background-color: #f5f7fa;
      text-align: center;
      margin: 0 auto;
      padding: 5px 10px;
      box-sizing: border-box;
      color: #333;
    }
    
    /* 客服悬浮按钮样式 */
    .customer-service {
      position: fixed;
      right: 20px;
      z-index: 999;
      bottom: 85px;
    }
    
    .cs-button {
      width: 65px;
      height: 65px;
      background: #4aa1ff;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(74, 161, 255, 0.25);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      border: none;
      padding: 0;
      box-sizing: border-box;
      animation: float 3s ease-in-out infinite;
    }
    
        .cs-button:hover {
       transform: scale(1.05);
        box-shadow: 0 6px 18px rgba(74, 161, 255, 0.35);
        background: #4aa1ff;
    }
    
    /* 响应式设计 - 桌面端 */
    @media screen and (min-width: 768px) {
      .cs-button {
        width: 55px;
        height: 55px;
      }
      
      .cs-button .iconfont {
        font-size: 20px !important;
      }
      
      .cs-button .cs-text {
        font-size: 10px !important;
      }
    }
    
    /* 响应式设计 - 移动端 */
    @media screen and (max-width: 767px) {
      .cs-button {
        width: 65px;
        height: 65px;
      }
      
      .cs-button .iconfont {
        font-size: 22px !important;
      }
      
      .cs-button .cs-text {
        font-size: 11px !important;
      }
    }
    
    .cs-text {
      font-size: 12px;
      color: white;
      font-weight: 500;
      margin-top: 5px;
    }
    
    @keyframes float {
      0% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-8px);
      }
      100% {
        transform: translateY(0px);
      }
    }
    
    .cs-popup {
      position: absolute;
      bottom: 90px;
      right: 0;
      left: auto;
      width: 280px;
      max-width: 85vw; /* 限制最大宽度，防止在小屏幕上溢出 */
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 5px 20px rgba(74, 161, 255, 0.12);
      border: 1px solid rgba(0, 0, 0, 0.05);
      padding: 25px 20px 20px;
      display: none;
      transform-origin: bottom right;
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      opacity: 0;
      transform: scale(0.8);
      text-align: center;
      box-sizing: border-box; /* 确保padding不会导致宽度计算问题 */
      z-index: 999;
    }
    
    .cs-popup.active {
      display: block;
      opacity: 1;
      transform: scale(1);
    }
    
    .cs-popup-title {
      margin-bottom: 20px;
      position: relative;
      display: inline-block;
    }
    
    .cs-popup-title:after {
      content: none;
    }
    
    .cs-popup-title-main {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 10px;
    }
    
    .cs-popup-title-sub {
      font-size: 14px;
      font-weight: 500;
      color: #555;
    }
    
    .cs-qrcode {
      width: 180px;
      height: 180px;
      margin: 10px auto 15px;
      border-radius: 8px;
      padding: 5px;
      border: 1px dashed #e0e0e0;
      position: relative;
      overflow: hidden;
    }
    
    .cs-qrcode img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }
    
    .cs-qrcode:before {
      content: '';
      position: absolute;
      top: -20px;
      left: -20px;
      right: -20px;
      bottom: -20px;
      background: linear-gradient(45deg, rgba(74, 161, 255, 0.1), transparent, rgba(74, 161, 255, 0.1));
      z-index: 1;
      animation: shine 3s infinite;
    }
    
    @keyframes shine {
      0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
      }
      100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
      }
    }
    
    .cs-group-number {
      font-size: 15px;
      color: #555;
      font-weight: 500;
      margin-bottom: 10px;
    }
    
    .cs-group-number span {
      color: #4aa1ff;
      font-weight: 600;
    }
    
    .cs-popup:after {
      content: '';
      position: absolute;
      bottom: -9px;
      right: 30px;
      width: 18px;
      height: 18px;
      background-color: white;
      transform: rotate(45deg);
      box-shadow: none;
      z-index: -1;
      border-right: 1px solid rgba(0, 0, 0, 0.05);
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .cs-close {
      position: absolute;
      top: 8px;
      right: 8px;
      color: #999;
      font-size: 22px;
      cursor: pointer;
      transition: all 0.2s;
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
    }
    
    .cs-close:hover {
      background-color: rgba(0, 0, 0, 0.03);
    }
    
    .cs-close:hover {
      color: #666;
    }
    
    /* 适配移动端 */
    @media screen and (max-width: 480px) {
      .customer-service {
        bottom: 70px;
        right: 15px;
      }
      
      .cs-button {
        width: 50px;
        height: 50px;
      }
      
      .cs-popup {
        width: 85vw; /* 使用视口宽度的百分比 */
        max-width: 290px; /* 最大宽度限制 */
        padding: 22px 18px 15px;
        right: 5px;
        left: auto;
        bottom: 80px;
        box-shadow: 0 5px 15px rgba(74, 161, 255, 0.12);
      }
      
      .cs-qrcode {
        width: 150px;
        height: 150px;
      }
      
      .cs-popup-title {
        margin-bottom: 15px;
      }
      
      .cs-popup-title-main {
        font-size: 17px;
        margin-bottom: 8px;
      }
    }
    
    /* 欢迎弹窗样式 */
    .welcome-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s, visibility 0.3s;
    }
    
    .welcome-overlay.show {
      opacity: 1;
      visibility: visible;
    }
    
    .welcome-popup {
      background-color: white;
      border-radius: 12px;
      width: 85%;
      max-width: 320px;
      padding: 25px 20px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
      position: relative;
      transform: translateY(20px);
      opacity: 0;
      transition: transform 0.4s, opacity 0.4s;
    }
    
    .welcome-overlay.show .welcome-popup {
      transform: translateY(0);
      opacity: 1;
    }
    
    .welcome-title {
      font-size: 20px;
      font-weight: 600;
      color: #4db7e9;
      margin-bottom: 25px;
    }
    
    .welcome-content {
      font-size: 15px;
      line-height: 1.5;
      color: #555;
      margin-bottom: 20px;
      text-align: left;
      padding: 0;
      max-width: 240px;
      margin-left: auto;
      margin-right: auto;
    }
    
    .welcome-qrcode {
      width: 180px;
      height: 180px;
      margin: 0 auto 15px;
      border-radius: 8px;
      overflow: hidden;
    }
    
    .welcome-qrcode img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .group-number {
      font-size: 16px;
      margin-bottom: 30px;
      color: #333;
      font-weight: 500;
    }
    
    .welcome-buttons {
      display: flex;
      justify-content: center;
      gap: 15px;
    }
    
    .join-group-btn {
      background-color: #4aa1ff;
      color: white;
      border: none;
      border-radius: 30px;
      padding: 10px 20px;
      font-size: 15px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .join-group-btn:hover {
      background-color: #3a8ed5;
      transform: translateY(-2px);
    }
    
    .close-welcome-btn {
      background-color: #f2f2f2;
      color: #666;
      border: none;
      border-radius: 30px;
      padding: 10px 20px;
      font-size: 15px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .close-welcome-btn:hover {
      background-color: #e5e5e5;
    }
    
    .main-form {
      background-color: white;
      padding: 0;
      border-radius: 16px;
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
      width: 100%;
      max-width: 460px;
      margin: 15px auto;
      box-sizing: border-box;
      overflow: hidden;
      border: none;
    }
    
    .form-header {
      background-color: transparent;
      padding: 30px 25px 15px;
      position: relative;
      border-bottom: none;
      margin: 0 0 5px 0;
      text-align: center;
    }
    
    .form-header h2 {
      color: #333;
      margin: 0;
      font-size: 32px;
      font-weight: 700;
      letter-spacing: -0.5px;
      background: #4aa1ff;
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      display: inline-block;
    }
    
    .form-header p {
      color: #666;
      margin: 20px 0 0;
      font-size: 15px;
      display: flex;
      align-items: center;
      position: relative;
      justify-content: center;
    }
    
    /* 删除底部分割线 */
    .form-header::after {
      display: none;
    }
    
    .form-content {
      padding: 5px 25px 25px;
    }
    
    .links {
      margin: 15px auto 30px;
      text-align: center;
      display: flex;
      justify-content: space-between;
      gap: 8px;
      width: 90%; /* 与输入框宽度一致 */
    }
    
    .link-button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 10px 12px;
      margin: 0;
      border-radius: 12px;
      color: white;
      text-decoration: none;
      font-size: 13px;
      font-weight: 500;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      transition: all 0.3s;
      backdrop-filter: blur(4px);
      -webkit-backdrop-filter: blur(4px);
      white-space: normal; /* 允许在必要时换行 */
      overflow: hidden;
      flex: 1;
      min-width: 0;
      line-height: 1.3; /* 适当行高，防止文字挤在一起 */
      min-height: 18px; /* 确保最小高度 */
    }
    
    .link-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.12);
    }
    
    .form-group {
      margin-bottom: 15px;
      text-align: center;
    }
    
    .form-input {
      width: 90%;
      height: 48px;
      padding: 12px 20px;
      border: 0.5px solid rgba(200, 210, 220, 0.6);
      border-radius: 12px;
      font-size: 16px;
      box-sizing: border-box;
      background-color: #fcfdff;
      color: #333;
      transition: all 0.2s;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
    }
    
    .form-input:focus {
      outline: none;
      background-color: #ffffff;
      border-color: #4aa1ff;
      box-shadow: 0 0 0 2px rgba(74, 161, 255, 0.08);
    }
    
    .form-input::placeholder {
      color: #999;
    }
    
    .code-wrapper {
      position: relative;
      width: 90%;
      margin: 0 auto;
      display: inline-block;
    }
    
    .code-input {
      padding-right: 120px;
      width: 100%;
    }
    
    .code-button {
      position: absolute;
      right: 6px;
      top: 6px;
      height: 36px;
      background-color: white;
      color: #07c160;
      border: 0.5px solid rgba(200, 210, 220, 0.6);
      border-radius: 10px;
      padding: 0 18px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
    }
    
    .code-button:hover {
      background-color: #f7f7f7;
      color: #06ad56;
    }
    
    .submit-button {
      width: 90%;
      height: 50px;
      background-color: #4aa1ff;
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      margin-top: 20px;
      box-shadow: 0 4px 12px rgba(74, 161, 255, 0.15);
      transition: all 0.3s;
    }
    
    .submit-button:hover {
      background-color: #3a8ed5;
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(74, 161, 255, 0.25);
    }
    
    .image-box {
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
      padding: 15px;
      width: 100%;
      max-width: 460px;
      margin: 20px auto 0;
      box-sizing: border-box;
    }
    
    .image-box p {
      margin: 0 0 15px;
      font-size: 16px;
      color: #444;
      font-weight: 500;
    }
    
    .image-box img {
      max-width: 100%;
      border-radius: 8px;
      transition: transform 0.3s;
    }
    
    .image-box img:hover {
      transform: scale(1.02);
    }
    
    .toast {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: rgba(51, 51, 51, 0.75);
      color: white;
      padding: 15px 25px;
      border-radius: 15px;
      font-size: 16px;
      z-index: 999999;
      min-width: 120px;
      text-align: center;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    
    .toast-icon {
      margin-bottom: 10px;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #ff4d4f;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .toast-icon svg {
      width: 20px;
      height: 20px;
    }
    
    .modal-backdrop {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 99999;
    }
    
    .modal-content {
      background-color: white;
      width: 90%;
      max-width: 320px;
      border-radius: 12px;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      text-align: center;
      overflow: hidden;
    }
    
    .modal-body {
      padding: 30px 20px 20px;
    }
    
    .modal-title {
      font-size: 20px;
      font-weight: 600;
      color: #333;
      margin: 0 0 10px;
    }
    
    .modal-message {
      font-size: 15px;
      color: #666;
      margin: 10px 0 0;
      line-height: 1.5;
    }
    
    .modal-footer {
      padding: 0 20px 20px;
    }
    
    .modal-button {
      background-color: #4db7e9;
      color: white;
      border: none;
      border-radius: 30px;
      padding: 10px 25px;
      font-size: 15px;
      cursor: pointer;
      min-width: 60%;
    }
    
    .modal-button:hover {
      background-color: #3aa8da;
    }
    
    /* 二维码弹窗样式 */
    .qrcode-container {
      width: 90%;
      max-width: 400px;
      background-color: white;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .qrcode-header {
      padding: 25px 20px 10px;
      text-align: center;
    }
    
    .qrcode-title {
      font-size: 22px;
      font-weight: 600;
      color: #333;
    }
    
    .qrcode-body {
      padding: 10px 20px 20px;
      text-align: center;
    }
    
    .qrcode-text {
      margin-bottom: 15px;
    }
    
    .qrcode-text p {
      margin: 8px 0;
      font-size: 15px;
      color: #555;
      text-align: left;
    }
    
    .qrcode-img {
      width: 200px;
      height: 200px;
      margin: 0 auto;
      border: 1px solid #eee;
      border-radius: 4px;
    }
    
    .qrcode-img img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .qrcode-footer {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0 15px 20px;
    }
    
    .qrcode-btn-join {
      width: 80%;
      padding: 12px 0;
      text-align: center;
      font-size: 16px;
      cursor: pointer;
      border-radius: 50px;
      background-color: #4db7e9;
      color: white;
      font-weight: 500;
      box-shadow: 0 4px 10px rgba(77, 183, 233, 0.2);
    }
    
    .qrcode-btn-join:hover {
      opacity: 0.9;
    }
    
    .qrcode-close-text {
      margin-top: 15px;
      font-size: 14px;
      color: #999;
      cursor: pointer;
    }
    
    .qrcode-close-text:hover {
      color: #666;
    }
    
    /* 简单模态框样式 */
    .simple-modal {
      background-color: white;
      width: 80%;
      max-width: 300px;
      border-radius: 12px;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      text-align: center;
      overflow: hidden;
      padding: 30px 25px;
      margin: 0 auto;
    }
    
    .simple-modal-icon {
      margin: 0 auto 20px;
      width: 50px;
      height: 50px;
    }
    
    .simple-modal-icon i {
      font-size: 48px;
    }
    
    .simple-modal-title {
      font-size: 22px;
      font-weight: 600;
      margin-bottom: 15px;
    }
    
    .simple-modal-content {
      font-size: 15px;
      color: #666;
      margin: 20px 0;
      line-height: 1.6;
    }
    
    .simple-modal-btn {
      display: inline-block;
      color: white;
      border: none;
      border-radius: 30px;
      padding: 12px 0;
      font-size: 16px;
      cursor: pointer;
      width: 65%;
      margin-top: 10px;
    }
    
    .simple-modal-btn:hover {
      opacity: 0.9;
    }
    
    @media (min-width: 768px) {
      body {
        padding: 20px 30px;
      }
    }
    
    @media screen and (max-width: 360px) {
      .link-button {
        padding: 8px 5px;
        font-size: 12px;
        font-weight: 400;
        letter-spacing: -0.3px;
      }
      
      .form-content {
        padding: 15px 20px 20px;
      }
    }
    
    /* 添加更小屏幕的特殊处理 */
    @media screen and (max-width: 320px) {
      .link-button {
        padding: 8px 4px;
        font-size: 11px;
        letter-spacing: -0.5px;
      }
    }
    
    /* 确保在iOS和Android上有一致的显示 */
    @supports (-webkit-touch-callout: none) {
      /* iOS特定样式 */
      .main-form, .image-box {
        width: 96%;
      }
      
      /* iOS弹窗美化 */
      .simple-modal {
        border-radius: 16px !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
      }
      
      .modal-content {
        border-radius: 16px !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
      }
      
      .qrcode-container {
        border-radius: 16px !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
      }
      
      /* 随机步数设置弹窗也有合适的尺寸 */
      .random-step-modal {
        border-radius: 16px !important;
      }
      
      /* 应用于所有弹窗的统一样式 */
      .modal-backdrop > * {
        margin: 0 auto;
        transform: translateY(-10px);
        animation: modal-appear 0.2s ease-out forwards;
      }
      
      @keyframes modal-appear {
        from {
          opacity: 0.8;
          transform: translateY(-10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    }
    
    @supports not (-webkit-touch-callout: none) {
      /* 非iOS(如Android)特定样式 */
      .main-form, .image-box {
        width: 96%;
      }
      
      /* 修正Android上的弹窗宽度 */
      @media (max-width: 768px) {
        .simple-modal {
          width: 78% !important;
          max-width: 320px !important;
          border-radius: 16px !important;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
        }
        
        .modal-content {
          width: 82% !important;
          max-width: 320px !important;
          border-radius: 16px !important;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
        }
        
        .qrcode-container {
          width: 92% !important;
          max-width: 380px !important;
          border-radius: 16px !important;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
        }
        
        .modal-backdrop .qrcode-container {
          width: 92% !important;
          max-width: 380px !important;
        }
        
        /* 确保随机步数设置弹窗也有合适的尺寸 */
        .random-step-modal {
          border-radius: 16px !important;
        }
        
        /* 应用于所有弹窗的统一样式 */
        .modal-backdrop > * {
          margin: 0 auto;
          transform: translateY(-10px);
          animation: modal-appear 0.2s ease-out forwards;
        }
        
        @keyframes modal-appear {
          from {
            opacity: 0.8;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      }
      
      /* 电脑端弹窗宽度 */
      @media (min-width: 769px) {
        .simple-modal {
          width: 400px !important;
          max-width: 450px !important;
        }
        
        .modal-content {
          width: 400px !important;
          max-width: 450px !important;
        }
        
        .qrcode-container {
          width: 400px !important;
          max-width: 450px !important;
        }
        
        .modal-backdrop .qrcode-container {
          width: 400px !important;
          max-width: 450px !important;
        }
      }
    }
    
    .content-wrapper {
      padding-bottom: 60px;
    }
    
    .nav-placeholder {
      display: none;
    }
    
    /* 加载动画样式 - 现代APP风格 */
    .loading-animation {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.85);
      backdrop-filter: blur(6px);
      -webkit-backdrop-filter: blur(6px);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    }
    
    .loading-gif {
      width: 120px;
      height: 120px;
      margin-bottom: 20px;
    }
    
    .loading-text {
      color: #666;
      font-size: 20px;
      font-weight: 600;
      margin-top: 10px;
      text-align: center;
    }
    
    .footer-runtime {
      padding: 15px 0 75px;
      text-align: center;
      color: #666;
      font-size: 13px;
      width: 100%;
    }
    
    .remember-me {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-top: 20px;
      color: #666;
      font-size: 13px;
    }
    
    .remember-me input[type="checkbox"] {
      width: 14px;
      height: 14px;
      margin: 0;
      cursor: pointer;
    }
    
    .remember-me label {
      cursor: pointer;
      user-select: none;
    }
    
    .modal-content .icon {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: #f8f9fa;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 16px;
      position: relative;
    }
    
    .modal-content .icon::before {
      content: '';
      width: 24px;
      height: 24px;
      border: 3px solid #dc3545;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    
    .modal-content .icon::after {
      content: '';
      width: 3px;
      height: 12px;
      background: #dc3545;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      transform-origin: bottom;
      animation: clockHand 2s linear infinite;
    }
    
    @keyframes clockHand {
      from {
        transform: translate(-50%, -50%) rotate(0deg);
      }
      to {
        transform: translate(-50%, -50%) rotate(360deg);
      }
    }
  </style>
  <script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?b772d25789abf751b026c58f720f17da";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script>

  <script charset="UTF-8" id="LA_COLLECT" src="//sdk.51.la/js-sdk-pro.min.js"></script>
  <script>LA.init({id:"3LglXyAXsLA0nwnf",ck:"3LglXyAXsLA0nwnf"})</script>
</head>

<body>
  <script>
    function noShare() {
      if (typeof WeixinJSBridge !== 'undefined') {
        WeixinJSBridge.call('hideOptionMenu');
      }
      
      document.addEventListener('WeixinJSBridgeReady', function() {
        WeixinJSBridge.call('hideOptionMenu');
      });
    }
    noShare();
    
    function showToast(message) {
      var toast = document.createElement('div');
      toast.className = 'toast';
      
      // 创建错误图标
      var iconDiv = document.createElement('div');
      iconDiv.className = 'toast-icon';
      
      // 添加X图标SVG
      var svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      svg.setAttribute('viewBox', '0 0 24 24');
      svg.innerHTML = '<path d="M18 6L6 18M6 6l12 12" stroke="white" stroke-width="2" stroke-linecap="round"/>';
      
      iconDiv.appendChild(svg);
      toast.appendChild(iconDiv);
      
      // 添加文本信息
      var text = document.createElement('div');
      text.innerText = message;
      toast.appendChild(text);
      
      document.body.appendChild(toast);
      
      setTimeout(function() {
        document.body.removeChild(toast);
      }, 2300);
    }
    
    function showModal(type, title, content, btnText) {
      var backdrop = document.createElement('div');
      backdrop.className = 'modal-backdrop';
      // 保留模糊背景效果
      backdrop.style.backgroundColor = "rgba(0,0,0,0.4)";
      backdrop.style.backdropFilter = "blur(4px)";
      backdrop.style.webkitBackdropFilter = "blur(4px)";
      
      var iconSvg = '';
      var titleColor = '';
      var btnColor = '';
      
      if (type === 'success' || type === '成功') {
        iconSvg = '<svg viewBox="0 0 24 24" fill="none" stroke="#52c41a" stroke-width="2"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><path d="M22 4L12 14.01l-3-3"></path></svg>';
        titleColor = '#52c41a';
        btnColor = '#52c41a';
      } else if (type === 'error' || type === '失败') {
        iconSvg = '<svg viewBox="0 0 24 24" fill="none" stroke="#ff4d4f" stroke-width="2"><circle cx="12" cy="12" r="10"></circle><path d="M15 9l-6 6"></path><path d="M9 9l6 6"></path></svg>';
        titleColor = '#ff4d4f';
        btnColor = '#ff4d4f';
      } else {
        iconSvg = '<svg viewBox="0 0 24 24" fill="none" stroke="#faad14" stroke-width="2"><circle cx="12" cy="12" r="10"></circle><path d="M12 8v4"></path><path d="M12 16h.01"></path></svg>';
        titleColor = '#faad14';
        btnColor = '#faad14';
      }
      
      // 恢复原始弹窗样式，保留渐变背景
      var modalHTML = '<div class="simple-modal" style="padding: 30px 25px; background: linear-gradient(to bottom, #ffffff, #f8f9fa); border-radius: 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);">';
      modalHTML += '<div class="simple-modal-icon" style="margin: 0 auto 20px; width: 50px; height: 50px;">' + iconSvg + '</div>';
      modalHTML += '<div class="simple-modal-title" style="color: ' + titleColor + '; margin-bottom: 15px; font-size: 22px;">' + title + '</div>';
      modalHTML += '<div class="simple-modal-content" style="margin: 20px 0; font-size: 15px;">' + content + '</div>';
      modalHTML += '<div class="simple-modal-btn" style="background-color: ' + btnColor + '; width: 65%; margin-top: 10px; padding: 12px 0;">' + btnText + '</div>';
      modalHTML += '</div>';
      
      backdrop.innerHTML = modalHTML;
      document.body.appendChild(backdrop);
      
      var closeBtn = backdrop.querySelector('.simple-modal-btn');
      closeBtn.addEventListener('click', function() {
        document.body.removeChild(backdrop);
      });
      
      // 添加点击弹窗外部区域关闭弹窗的功能
      backdrop.addEventListener('click', function(e) {
        if (e.target === backdrop) {
          document.body.removeChild(backdrop);
        }
      });
    }
    
    function showQRCode() {
      // 记录当前滚动位置
      var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      
      // 创建弹窗HTML
      var html = '<div class="modal-backdrop" id="qrcodeModal" style="z-index:999999">';
      html += '<div class="qrcode-container" style="z-index:1000000">';
      html += '<div class="qrcode-header"><div class="qrcode-title">加Q群获取验证码</div></div>';
      html += '<div class="qrcode-body">';
      html += '<div class="qrcode-text">';
      html += '<p style="font-size:15px;color:#666;margin-bottom:25px;">加Q群方式：扫码或者点击一键加群按钮</p>';
      html += '<p style="margin:12px 0 15px;font-size:16px;"><span style="color:#ff6a3c;font-weight:600;">为什么要加群？</span></p>';
      html += '<p style="margin-bottom:10px;"><span style="color:#4db7e9;font-weight:600;">原因：防止失联</span>，<span style="color:#555;">即使遇到不可抗因素导致现有网址无法打开，您还能通过我们的Q群获取新的网址。</span></p>';
      html += '</div>';
      html += '<div class="qrcode-img">';
      html += '<img src="images/qun1.jpg">';
      html += '</div>';
      html += '</div>';
      html += '<div class="qrcode-footer">';
      html += '<a href="https://qm.qq.com/q/aDE0afaXsI" target="_blank" class="qrcode-btn-join" style="text-decoration:none;display:block;">一键加群</a>';
      html += '<div class="qrcode-close-text" id="closeQrcodeButton">关闭</div>';
      html += '</div>';
      html += '</div>';
      html += '</div>';
      
      // 直接插入HTML
      var div = document.createElement('div');
      div.innerHTML = html;
      var modal = div.firstChild;
      document.body.appendChild(modal);
      
      // 防止页面滚动
      document.body.style.overflow = 'hidden';
      // 恢复原来的滚动位置
      window.scrollTo(0, scrollTop);
      
      // 关闭弹窗并恢复滚动的函数
      function closeQrcodeModal() {
        document.body.removeChild(modal);
        // 恢复页面滚动能力
        document.body.style.overflow = 'auto';
        // 平滑滚动到原来的位置
        window.scrollTo({
          top: scrollTop,
          behavior: 'smooth'
        });
      }
      
      // 单独注册关闭按钮的点击事件
      document.getElementById('closeQrcodeButton').addEventListener('click', function(event) {
        event.preventDefault();
        closeQrcodeModal();
      });
      
      // 点击背景关闭
      modal.addEventListener('click', function(e) {
        if (e.target === modal) {
          closeQrcodeModal();
        }
      });
    }
  </script>

  <!-- 添加全局可访问的cookie函数 -->
  <script>
    // Cookie操作函数
    function setCookie(name, value, days) {
      var expires = "";
      if (days) {
        var date = new Date();
        date.setTime(date.getTime() + (days*24*60*60*1000));
        expires = "; expires=" + date.toUTCString();
      }
      document.cookie = name + "=" + (value || "")  + expires + "; path=/";
    }
    
    function getCookie(name) {
      var nameEQ = name + "=";
      var ca = document.cookie.split(';');
      for(var i=0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) == ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
      }
      return null;
    }
    
    // 生成随机步数函数
    function generateRandomStep() {
      var min = parseInt(getCookie('random_step_min') || '10000');
      var max = parseInt(getCookie('random_step_max') || '30000');
      
      if (isNaN(min) || min < 0) min = 10000;
      if (isNaN(max) || max < min) max = min + 20000;
      
      var randomStep = Math.floor(Math.random() * (max - min + 1)) + min;
      document.querySelector('input[name="step"]').value = randomStep;
    }

    // 应用随机步数设置
    function applyRandomStepSetting() {
      try {
        var randomStepEnabled = getCookie('random_step_enabled') === 'true';
        if (randomStepEnabled) {
          generateRandomStep();
        }
      } catch (e) {
        console.error('应用随机步数设置失败:', e);
      }
    }
    
    // 在showModal函数后添加随机步数设置弹窗函数
    function showRandomStepSettings() {
      try {
        // 获取当前设置
        var randomStepEnabled = getCookie('random_step_enabled') === 'true';
        var minStep = getCookie('random_step_min') || '10000';
        var maxStep = getCookie('random_step_max') || '30000';
        
        var backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop';
        // 添加模糊背景效果
        backdrop.style.backgroundColor = "rgba(0,0,0,0.4)";
        backdrop.style.backdropFilter = "blur(4px)";
        backdrop.style.webkitBackdropFilter = "blur(4px)";
        
        // 首先添加CSS样式到页面
        var styleEl = document.createElement('style');
        styleEl.innerHTML = `
          .random-step-modal {
            padding: 25px;
            text-align: left;
            border-radius: 15px;
          }
          .random-step-header {
            text-align: center;
            margin-bottom: 25px;
          }
          .random-step-title {
            margin: 0 0 10px;
            font-size: 20px;
            color: #333;
            font-weight: 600;
          }
          .random-step-subtitle {
            margin: 0;
            font-size: 14px;
            color: #888;
            line-height: 1.4;
          }
          .random-step-section {
            margin-bottom: 25px;
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 10px;
          }
          .random-step-toggle {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
          }
          .random-step-label {
            font-size: 16px;
            color: #444;
            font-weight: 500;
          }
          .random-step-range {
            margin-bottom: 8px;
            font-size: 16px;
            color: #444;
            font-weight: 500;
          }
          .random-step-inputs {
            display: flex;
            align-items: center;
          }
          .random-step-input {
            width: 40%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 15px;
            background-color: white;
          }
          .random-step-separator {
            margin: 0 10px;
            color: #888;
            font-size: 14px;
          }
          .random-step-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 25px;
          }
          .random-step-button {
            width: 48%;
            padding: 12px 0;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
          }
          .random-step-button-cancel {
            background-color: #f5f5f5;
            color: #666;
          }
          .random-step-button-save {
            background-color: #4db7e9;
            color: white;
          }
          .random-step-button:hover {
            opacity: 0.9;
          }
          
          /* 开关样式 */
          .rs-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 26px;
          }
          .rs-switch input {
            opacity: 0;
            width: 0;
            height: 0;
          }
          .rs-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 26px;
          }
          .rs-slider:before {
            position: absolute;
            content: "";
            height: 20px;
            width: 20px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
          }
          input:checked + .rs-slider {
            background-color: #4db7e9;
          }
          input:focus + .rs-slider {
            box-shadow: 0 0 1px #4db7e9;
          }
          input:checked + .rs-slider:before {
            transform: translateX(24px);
          }
        `;
        document.head.appendChild(styleEl);
        
        var modalHTML = `
          <div class="modal-content random-step-modal">
            <div class="random-step-header">
              <div class="random-step-title">随机步数设置</div>
              <div class="random-step-subtitle">开启后每次访问将自动生成随机步数</div>
            </div>
            
            <div class="random-step-section">
              <div class="random-step-toggle">
                <span class="random-step-label">启用随机步数</span>
                <label class="rs-switch">
                  <input type="checkbox" id="randomStepSwitch" ${randomStepEnabled ? 'checked' : ''}>
                  <span class="rs-slider"></span>
                </label>
              </div>
              
              <div class="random-step-range">步数范围设置</div>
              <div class="random-step-inputs">
                <input type="number" id="minStepInput" placeholder="最小值" value="${minStep}" class="random-step-input">
                <span class="random-step-separator">至</span>
                <input type="number" id="maxStepInput" placeholder="最大值" value="${maxStep}" class="random-step-input">
              </div>
            </div>
            
            <div class="random-step-buttons">
              <button id="cancelRandomBtn" class="random-step-button random-step-button-cancel">取消</button>
              <button id="saveRandomBtn" class="random-step-button random-step-button-save">保存</button>
            </div>
          </div>
        `;
        
        backdrop.innerHTML = modalHTML;
        document.body.appendChild(backdrop);
        
        // 清除临时样式元素的函数
        function removeStyleElement() {
          if (document.head.contains(styleEl)) {
            document.head.removeChild(styleEl);
          }
        }
        
        // 保存按钮点击事件
        document.getElementById('saveRandomBtn').addEventListener('click', function() {
          var enabled = document.getElementById('randomStepSwitch').checked;
          var min = document.getElementById('minStepInput').value || '10000';
          var max = document.getElementById('maxStepInput').value || '30000';
          
          // 验证输入
          min = parseInt(min);
          max = parseInt(max);
          
          if (isNaN(min) || min < 0) {
            min = 10000;
          }
          
          if (isNaN(max) || max < min) {
            max = min + 20000;
          }
          
          // 保存设置到cookie
          setCookie('random_step_enabled', enabled, 30);
          setCookie('random_step_min', min, 30);
          setCookie('random_step_max', max, 30);
          
          // 如果启用，立即生成随机步数；如果禁用，清空步数输入框
          if (enabled) {
            generateRandomStep();
          } else {
            // 清空步数输入框
            document.querySelector('input[name="step"]').value = '';
          }
          
          // 关闭弹窗
          document.body.removeChild(backdrop);
          removeStyleElement();
        });
        
        // 取消按钮点击事件
        document.getElementById('cancelRandomBtn').addEventListener('click', function() {
          document.body.removeChild(backdrop);
          removeStyleElement();
        });
        
        // 点击背景关闭
        backdrop.addEventListener('click', function(e) {
          if (e.target === backdrop) {
            document.body.removeChild(backdrop);
            removeStyleElement();
          }
        });
      } catch (e) {
        console.error('显示随机步数设置失败:', e);
        alert('显示设置失败，请刷新页面重试');
      }
    }
  </script>

  <div class="content-wrapper">
    <div class="main-form">
      <div class="form-header">
        <h2>小麦刷步网</h2>
        <p><span style="display:inline-flex;align-items:center;justify-content:center;width:18px;height:18px;background:linear-gradient(135deg, #34C759, #30D158);border-radius:50%;margin-right:8px;box-shadow:0 2px 6px rgba(52, 199, 89, 0.25);"><i class="fas fa-bolt" style="font-size:10px;color:white;"></i></span>独家技术确保同步</p>
      </div>
      
      <div class="form-content">
        <div class="links">
          <a href="javascript:void(0);" onclick="showTutorialQRCode()" class="link-button" style="background: linear-gradient(135deg, #FF9F0A, #FF7D26);">
            使用教程
          </a>
          <a href="javascript:void(0);" onclick="showMiniProgramQRCode()" class="link-button" style="background: linear-gradient(135deg, #60afff, #5aa5f0);">
            小程序版
          </a>
          <a href="https://myxiu.site" class="link-button" style="background: linear-gradient(135deg, #34C759, #30D158);">
            自动刷步
          </a>
        </div>
        
        <form id="stepForm">
          <div class="form-group">
            <div class="code-wrapper" style="position: relative; width: 90%; margin: 0 auto; display: inline-block;">
              <input type="text" name="user" placeholder="邮箱/手机号" class="form-input code-input">
              <button type="button" class="code-button" id="switchAccountBtn" style="background-color: #ffffff; color: #07c160; transition: all 0.2s ease;">
                <i class="fas fa-chevron-down" style="font-size: 14px; transition: transform 0.3s ease;"></i>
              </button>
            </div>
          </div>
          
          <div class="form-group">
            <div class="code-wrapper">
              <input type="password" name="password" placeholder="密码" class="form-input code-input">
              <button type="button" class="password-toggle-btn" style="position: absolute; right: 6px; top: 6px; height: 36px; background: none; border: none; cursor: pointer; color: #999; padding: 0 15px;">
                <i class="fas fa-eye-slash"></i>
              </button>
            </div>
          </div>
          
          <div class="form-group">
            <div class="code-wrapper" style="position: relative; width: 90%; margin: 0 auto; display: inline-block;">
              <input type="text" name="step" placeholder="步数" class="form-input code-input">
              <button type="button" class="code-button" id="randomStepBtn" style="background-color: #ffffff; color: #07c160;">
                <i class="fas fa-random" style="font-size: 14px;"></i>
              </button>
            </div>
          </div>
          
          <div class="form-group" style="text-align: left; padding-left: 8%; padding-right: 8%; margin-bottom: 12px; margin-top: 10px; display: flex; justify-content: space-between; align-items: center;">
            <div class="remember-me" style="margin-top: 0;">
              <input type="checkbox" id="rememberMe" name="rememberMe">
              <label for="rememberMe">记住密码</label>
            </div>
            <a href="faq.html" style="font-size: 13px; color: #4db7e9; text-decoration: none;">常见问题</a>
          </div>
          
          <button type="submit" class="submit-button" id="submitBtn">提交刷步</button>
        </form><br/>

        <!-- 账号切换弹窗 -->
        <div class="account-switch-modal" id="accountSwitchModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.4); backdrop-filter: blur(4px); -webkit-backdrop-filter: blur(4px); z-index: 9999; display: none; align-items: center; justify-content: center;">
          <div class="account-switch-container" style="width: 90%; max-width: 350px; background: white; border-radius: 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15); padding: 30px 20px 20px; position: relative;">
            <div class="account-switch-header" style="text-align: center; margin-bottom: 25px;">
              <h3 style="margin: 0; font-size: 22px; font-weight: 600; background: linear-gradient(135deg, #4aa1ff, #4db7e9); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; margin-bottom: 15px;">切换刷步账号</h3>
              <p style="margin: 5px 0 0; color: #666; font-size: 14px;">选择已保存的账号或添加新账号</p>
            </div>
            
            <div class="account-list-wrapper" style="background-color: #f9fafb; border-radius: 12px; padding: 8px; margin-bottom: 15px;">
              <div class="account-list" id="accountList" style="max-height: 230px; overflow-y: auto;">
                <!-- 账号列表将通过JavaScript动态生成 -->
              </div>
            </div>
            
            <!-- 添加账号表单，默认隐藏 -->
            <div id="addAccountForm" style="display: none; margin-top: 15px; border-top: 1px solid #f2f2f2; padding-top: 15px;">
              <div style="margin-bottom: 15px; text-align: center;">
                <h4 style="margin: 0 0 15px; font-size: 18px; color: #333; font-weight: 600;">添加新账号</h4>
              </div>
              <div style="margin-bottom: 15px;">
                <input type="text" id="newAccountInput" placeholder="邮箱/手机号" style="width: 100%; height: 45px; padding: 10px 15px; border: 1px solid #ddd; border-radius: 10px; box-sizing: border-box; font-size: 15px; background-color: #f9f9fb; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);">
              </div>
              <div style="margin-bottom: 20px;">
                <input type="password" id="newPasswordInput" placeholder="密码" style="width: 100%; height: 45px; padding: 10px 15px; border: 1px solid #ddd; border-radius: 10px; box-sizing: border-box; font-size: 15px; background-color: #f9f9fb; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);">
              </div>
              <div style="display: flex; gap: 10px;">
                <button id="cancelAddBtn" style="flex: 1; background-color: #f5f5f5; color: #666; border: none; border-radius: 10px; padding: 12px 0; font-size: 15px; cursor: pointer; transition: all 0.2s ease;">取消</button>
                <button id="confirmAddBtn" style="flex: 1; background-color: #4aa1ff; color: white; border: none; border-radius: 10px; padding: 12px 0; font-size: 15px; cursor: pointer; transition: all 0.2s ease; box-shadow: 0 4px 12px rgba(74, 161, 255, 0.15);">保存</button>
              </div>
            </div>
            
            <div class="account-switch-footer" id="addAccountBtnContainer" style="padding-top: 20px; padding-bottom: 5px;">
              <button id="addAccountBtn" style="background-color: #4aa1ff; color: white; border: none; border-radius: 25px; padding: 12px 20px; font-size: 15px; cursor: pointer; min-width: 60%; display: block; margin: 0 auto;">
                <i class="fas fa-plus" style="margin-right: 5px;"></i> 添加新账号
              </button>
            </div>
            
            <div class="account-switch-close" style="position: absolute; top: 15px; right: 15px; cursor: pointer; color: #999; font-size: 20px;">
              <i class="fas fa-times"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
  // 页面初始化和功能整合 - 简化版本
  document.addEventListener('DOMContentLoaded', function() {
    // 页面初始化功能整合（保留原有有用功能，删除多余的备用事件绑定）

    // URL参数处理和新用户检查
    function getUrlParam(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return decodeURIComponent(r[2]); return null;
    }

    var refer = getUrlParam('refer');
    if (refer) {
        document.cookie = "refer=" + refer + ";path=/;max-age=" + 30*24*60*60; // 30天有效期
    }

    // 检查新用户
    function checkNewUser() {
      if (!localStorage.getItem('xiaomaiVisited')) {
        localStorage.setItem('xiaomaiVisited', 'true');
        setTimeout(function() {
          var overlay = document.getElementById('welcomeOverlay');
          if (overlay) {
            overlay.style.opacity = "1";
            overlay.style.visibility = "visible";
            setTimeout(function() {
              var popup = overlay.querySelector('.welcome-popup');
              if (popup) {
                popup.style.opacity = "1";
                popup.style.transform = "translateY(0)";
              }
            }, 100);
          }
        }, 1000);
      }
    }

    // 关闭弹窗事件
    var closeWelcomeBtn = document.getElementById('closeWelcomeBtn');
    if (closeWelcomeBtn) {
      closeWelcomeBtn.addEventListener('click', function() {
        if (typeof closeWelcomePopup === 'function') {
          closeWelcomePopup();
        }
      });
    }

    checkNewUser();

    // 功能2：弹窗背景关闭和QQ群按钮
    var welcomeOverlay = document.getElementById('welcomeOverlay');
    if (welcomeOverlay) {
      welcomeOverlay.addEventListener('click', function(e) {
        if (e.target === this) {
          if (typeof closeWelcomePopup === 'function') {
            closeWelcomePopup();
          }
        }
      });
    }

    // 关闭欢迎弹窗的函数
    function closeWelcomePopup() {
      var overlay = document.getElementById('welcomeOverlay');
      if (overlay) {
        var popup = overlay.querySelector('.welcome-popup');
        if (popup) {
          popup.style.opacity = "0";
          popup.style.transform = "translateY(20px)";
        }
        setTimeout(function() {
          overlay.style.opacity = "0";
          overlay.style.visibility = "hidden";
        }, 300);
      }
    }

    // 加入QQ群按钮点击事件
    var joinGroupBtn = document.getElementById('joinGroupBtn');
    if (joinGroupBtn) {
      joinGroupBtn.addEventListener('click', function() {
        window.location.href = 'https://qm.qq.com/q/aDE0afaXsI';
      });

      // 添加按钮悬浮效果
      joinGroupBtn.addEventListener('mouseover', function() {
        this.style.transform = 'translateY(-2px)';
        this.style.boxShadow = '0 8px 20px rgba(74, 161, 255, 0.3)';
      });

      joinGroupBtn.addEventListener('mouseout', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = '0 5px 15px rgba(74, 161, 255, 0.2)';
      });
    }

    // 功能3：客服按钮交互
    var csButton = document.getElementById('csButton');
    var csPopup = document.getElementById('csPopup');
    var overlay = document.getElementById('overlay');
    var csClose = document.getElementById('csClose');

    if (csButton && csPopup && overlay) {
      csButton.addEventListener('click', function(e) {
        e.stopPropagation();
        csPopup.classList.toggle('active');
        overlay.style.display = csPopup.classList.contains('active') ? 'block' : 'none';
      });
    }

    if (csClose && csPopup && overlay) {
      csClose.addEventListener('click', function(e) {
        e.stopPropagation();
        csPopup.classList.remove('active');
        overlay.style.display = 'none';
      });
    }

    if (overlay && csPopup) {
      overlay.addEventListener('click', function() {
        csPopup.classList.remove('active');
        overlay.style.display = 'none';
      });
    }

    // 点击页面其他地方关闭弹窗
    document.addEventListener('click', function(e) {
      if (csPopup && overlay && !e.target.closest('.customer-service') && e.target.id !== 'overlay') {
        csPopup.classList.remove('active');
        overlay.style.display = 'none';
      }
    });
  });

  layui.use('layer', function(){
    var layer = layui.layer;
    
    layui.use('jquery', function() {
      var $ = layui.$;
      
      // 随机步数按钮点击事件
      document.getElementById('randomStepBtn').onclick = function(e) {
        e.preventDefault();
        showRandomStepSettings();
      };
      
      // 表单提交
      document.getElementById('stepForm').onsubmit = function(e) {
        e.preventDefault();
        
        // 获取表单数据
        var user = document.querySelector('input[name="user"]').value;
        var password = document.querySelector('input[name="password"]').value;
        var step = document.querySelector('input[name="step"]').value;
        
        if (!user) {
          showToast('请输入邮箱');
          return false;
        }
        
        // 验证邮箱格式
        // 原始验证邮箱格式的代码
        // const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        // if (!emailRegex.test(email)) {
        //   showToast('请输入有效的邮箱地址');
        //   return;
        // }

        // 修改后的验证：同时支持邮箱和手机号格式
        const emailOrPhoneRegex = /^(?:[^\s@]+@[^\s@]+\.[^\s@]+$|1[3-9]\d{9})$/;
        if (!emailOrPhoneRegex.test(user)) {
          showToast('请输入有效的邮箱地址或手机号');
          return false;
        }
        
        if (!password) {
          showToast('请输入密码');
          return false;
        }
        
        if (!step) {
          showToast('请输入步数');
          return false;
        }
        
        // 显示加载动画
        showLoading('正在努力为您刷步中...');
        
        // 获取提交按钮并设置状态
        var submitBtn = document.getElementById('submitBtn');
        submitBtn.disabled = true;
        submitBtn.innerText = '提交中...';
        
        // 使用GET方式请求step_proxy.php
        $.ajax({
          type: 'GET',
          url: "step_proxy.php",
          data: {
            user: user,
            password: password,
            step: step
          },
          dataType: 'json',
          success: function(response) {
            console.log("API返回:", response);
            // 隐藏加载动画
            hideLoading();
            
            // 恢复按钮状态
            submitBtn.disabled = false;
            submitBtn.innerText = '提交刷步';
            
            if (response.code === 'NO_MEMBERSHIP') {
              // 会员已过期或未开通
              showMembershipModal('您的刷步会员已过期', response.message, '开通会员');
            } else if (response.success) {
              // 刷步成功
              let successMessage = '已成功修改步数为' + step + '！';
              
              // 成功刷步后才保存用户凭据（如果勾选了"记住我"）
              saveUserCredentials();
              
              // 保存到账号切换列表
              
              // 如果返回了会员信息，则显示
              if (response.membership && response.membership.type) {
                const membershipTypeMap = {
                  'trial': '试用会员',
                  'monthly': '月度会员',
                  'quarterly': '季度会员',
                  'halfyear': '半年会员',
                  'yearly': '年度会员',
                  'permanent': '永久会员'
                };
                const membershipType = membershipTypeMap[response.membership.type] || '会员';
                
                // 对永久会员进行特殊处理
                if (response.membership.type === 'permanent' || response.membership.is_permanent) {
                  successMessage += `<br><br>您是${membershipType}，<span style="color: gold; font-weight: bold;">永久有效</span>`;
                } else {
                  const daysLeft = response.membership.days_left || 0;
                  successMessage += `<br><br>您是${membershipType}，剩余${daysLeft}天`;
                }
              }
              
              showModal('success', '提交成功', successMessage, '完成');
              // 保存邮箱到cookie，设置7天过期
              document.cookie = "user_email=" + user + ";path=/;max-age=" + 7 * 24 * 60 * 60;
            } else {
              // 其他错误
              if (response.message && (response.message.includes('账号或密码错误') || response.message.includes('请正确填写账号或密码'))) {
                showModal('error', '账号或密码错误', response.message || '刷步失败！', '关闭');
              } else {
                showModal('error', '提交失败', '刷步失败：' + response.message || '刷步失败！', '关闭');
              }
            }
          },
          error: function(xhr, status, error) {
            console.error("请求失败:", error);
            // 隐藏加载动画
            hideLoading();
            
            // 恢复按钮状态
            submitBtn.disabled = false;
            submitBtn.innerText = '提交刷步';
            showModal('info', '请求失败', '网络连接错误，请稍后重试', '关闭');
          }
        });
        
        return false;
      };
      
      // 会员弹窗
      function showMembershipModal(title, message, btnText) {
        var backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop';
        // 保留模糊背景效果
        backdrop.style.backgroundColor = "rgba(0,0,0,0.4)";
        backdrop.style.backdropFilter = "blur(4px)";
        backdrop.style.webkitBackdropFilter = "blur(4px)";
        
            // 创建全新的警告图标SVG
  var iconSvg = '<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" stroke="#ff6b6b" stroke-width="2"/><path d="M12 7V13" stroke="#ff6b6b" stroke-width="2" stroke-linecap="round"/><circle cx="12" cy="16" r="1.25" fill="#ff6b6b"/></svg>';
  var titleColor = '#ff6b6b';
  var btnColor = '#ff6b6b';
  var inviteBtnColor = '#4aa1ff';
        
        // 恢复原始样式，保留渐变背景
        var modalHTML = '<div class="simple-modal" style="padding: 30px 25px; background: linear-gradient(to bottom, #ffffff, #f8f9fa); border-radius: 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);">';
        modalHTML += '<div class="simple-modal-icon" style="margin: 0 auto 20px; width: 50px; height: 50px;">' + iconSvg + '</div>';
        modalHTML += '<div class="simple-modal-title" style="color: ' + titleColor + '; font-size: 22px; margin-bottom: 15px;">' + title + '</div>';
        modalHTML += '<div class="simple-modal-content" style="font-size: 15px; margin: 20px 0;">' + message + '</div>';
        modalHTML += '<div class="simple-modal-btn" style="background-color: ' + btnColor + '; margin-bottom: 12px; width: 65%; padding: 12px 0;" id="buyMemberBtn">' + btnText + '</div>';
        modalHTML += '<div class="simple-modal-btn" style="background-color: ' + inviteBtnColor + '; margin-bottom: 0; width: 65%; padding: 12px 0;" id="useMiniProgramBtn">使用小程序</div>';
        modalHTML += '</div>';
        
        backdrop.innerHTML = modalHTML;
        document.body.appendChild(backdrop);
        
        // 开通会员按钮点击事件
        document.getElementById('buyMemberBtn').addEventListener('click', function() {
          window.location.href = 'payment.html';
          document.body.removeChild(backdrop);
        });
        
        // 使用小程序按钮点击事件
        document.getElementById('useMiniProgramBtn').addEventListener('click', function() {
          // 不关闭会员过期弹窗，直接显示小程序二维码
          showMiniProgramQRCode(); // 显示小程序二维码弹窗
        });
        
        // 点击背景关闭弹窗
        backdrop.addEventListener('click', function(e) {
          if (e.target === backdrop) {
            document.body.removeChild(backdrop);
          }
        });
      }
      
      // 在窗口完全加载后再次尝试填写，确保万无一失
      window.addEventListener('load', function() {
        loadUserCredentials();
        applyRandomStepSetting();
      });
      
      // 在setCookie函数之后添加函数来保存和加载账号密码
      // 暴露到全局作用域，以便后续覆盖
      window.saveUserCredentials = function() {
        try {
          var rememberMe = document.getElementById('rememberMe').checked;
          var user = document.querySelector('input[name="user"]').value;
          var password = document.querySelector('input[name="password"]').value;
          
          if (rememberMe && user && password) {
            setCookie('remember_me', 'true', 30);
            setCookie('remembered_user', user, 30);
            setCookie('remembered_password', password, 30);
            console.log('保存用户凭据成功');
          } else if (!rememberMe) {
            // 如果取消勾选，删除已保存的凭据
            document.cookie = "remember_me=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT";
            document.cookie = "remembered_user=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT";
            document.cookie = "remembered_password=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT";
            console.log('已清除保存的用户凭据');
          }
        } catch (e) {
          console.error('保存用户凭据失败：', e);
        }
      }
      
      function loadUserCredentials() {
        try {
          var rememberMe = getCookie('remember_me');
          if (rememberMe === 'true') {
            var user = getCookie('remembered_user');
            var password = getCookie('remembered_password');
            
            if (user) {
              document.querySelector('input[name="user"]').value = user;
            }
            
            if (password) {
              document.querySelector('input[name="password"]').value = password;
            }
            
            // 勾选"记住我"复选框
            document.getElementById('rememberMe').checked = true;
            console.log('加载用户凭据成功');
          }
        } catch (e) {
          console.error('加载用户凭据失败：', e);
        }
      }
    });
  });
  </script>
  
  <!-- 底部导航 -->
  <nav class="bottom-nav">
    <a href="index.html" class="nav-item active">
      <div class="nav-icon"><i class="fas fa-walking"></i></div>
      <div>刷步</div>
    </a>
    <a href="payment.html" class="nav-item">
      <div class="nav-icon member-icon">&#x10131;</div>
      <div>开通会员</div>
    </a>
    <a href="membership.html" class="nav-item">
      <div class="nav-icon"><i class="fas fa-user"></i></div>
      <div>我的</div>
    </a>
  </nav>
  
  <!-- 遮罩层 -->
  <div id="overlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.4); backdrop-filter: blur(4px); -webkit-backdrop-filter: blur(4px); z-index: 998;"></div>
  
  <!-- 客服悬浮按钮 -->
  <div class="customer-service" style="bottom: 85px;">
    <div class="cs-button" id="csButton" style="background: #4aa1ff; border: none; box-shadow: 0 4px 12px rgba(74, 161, 255, 0.25); position: relative; transition: all 0.3s ease;">
      <i class="iconfont" style="color: white; font-size: 22px; margin-bottom: 0; line-height: 1;">&#xe89c;</i>
      <div class="cs-text" style="margin-top: 2px; line-height: 1; font-size: 11px;">客服</div>
    </div>
    <div class="cs-popup" id="csPopup" style="padding: 20px 15px; border-radius: 16px; box-shadow: 0 5px 20px rgba(74, 161, 255, 0.12); border: 1px solid rgba(0, 0, 0, 0.05); width: 85vw; max-width: 300px; right: 20px; left: auto; z-index: 999;">
      <div class="cs-close" id="csClose" style="top: 8px; right: 8px;">
        <span style="font-size: 22px; font-weight: 400;">&times;</span>
      </div>
      <div style="text-align: center; word-wrap: break-word; overflow-wrap: break-word;">
        <div style="font-size: 20px; font-weight: 600; color: #4aa1ff; margin-bottom: 20px;">
          <i class="iconfont" style="margin-right: 8px; font-size: 24px; vertical-align: middle;">&#xe89c;</i>联系客服
        </div>
        <div style="margin-bottom: 15px; text-align: center;">
          <div style="color: #333; margin-bottom: 8px; font-size: 14px;">有问题请到Q群找群主解决，</div>
          <div style="color: #333; font-size: 14px; margin-bottom: 12px;">这是售后问题唯一的处理途径</div>
          <div class="qrcode-img" style="margin: 10px auto; display: flex; justify-content: center; width: 170px; height: 170px; padding: 4px; background: white; border-radius: 6px; box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);">
            <img src="images/qun1.jpg" style="width: 100%; height: 100%;" alt="QQ群二维码">
          </div>
          <div style="color: #666; font-size: 13px; margin-top: 8px;">
            扫码或点击下方按钮加Q群
          </div>
        </div>
        <a href="https://qm.qq.com/q/aDE0afaXsI" target="_blank" style="display: inline-block; background: #4aa1ff; color: white; padding: 10px 22px; border-radius: 25px; text-decoration: none; font-weight: 500; box-shadow: 0 4px 12px rgba(74, 161, 255, 0.25); transition: all 0.3s ease; font-size: 15px;">
          一键加群
        </a>
      </div>
    </div>
  </div>
  
  <!-- 欢迎弹窗 -->
  <div class="welcome-overlay" id="welcomeOverlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.4); backdrop-filter: blur(4px); -webkit-backdrop-filter: blur(4px); z-index: 9999; display: flex; align-items: center; justify-content: center; opacity: 0; visibility: hidden; transition: opacity 0.3s, visibility 0.3s;">
    <div class="welcome-popup" style="text-align:center; background: linear-gradient(to bottom, #ffffff, #f8f9fa); border-radius: 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); width: 85%; max-width: 320px; transform: translateY(20px); opacity: 0; transition: transform 0.4s, opacity 0.4s;">
      <div class="welcome-header" style="padding: 15px 20px 5px;">
        <div class="welcome-title" style="text-align:center; font-size: 22px; font-weight: 600; background: linear-gradient(135deg, #4aa1ff, #4db7e9); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; margin-bottom: 15px;">欢迎使用小麦刷步</div>
      </div>
      <div class="welcome-body" style="text-align:center; padding: 5px 20px 12px;">
        <div class="welcome-content" style="font-size: 15px; line-height: 1.5; color: #555; margin-bottom: 12px; text-align: center;">
        本站已安全提供优质服务多年，<br>新用户请加Q群查看刷步教程。
      </div>
        <div class="welcome-qrcode" style="margin:0 auto; display:flex; justify-content:center; width: 180px; height: 180px; padding: 8px; background: white; border-radius: 12px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">
          <img src="images/qun1.jpg" style="width: 100%; height: 100%; border-radius: 6px;" alt="QQ群二维码">
      </div>
        <div class="group-number" style="margin-top: 12px; font-size: 15px; color: #333; font-weight: 500;">QQ群号：<span style="color:#4aa1ff;font-weight:600;">698912745</span></div>
      </div>
      <div class="welcome-footer" style="text-align:center; display:flex; flex-direction:column; align-items:center; padding: 0 20px 5px; margin-top: -10px;">
        <button id="joinGroupBtn" class="join-group-btn" style="width: 85%; background: linear-gradient(135deg, #4aa1ff, #4db7e9); color: white; border: none; border-radius: 25px; padding: 10px 0; font-size: 16px; margin-bottom: 15px; cursor: pointer; display: inline-block; box-shadow: 0 5px 15px rgba(74, 161, 255, 0.2); font-weight: 500; transition: all 0.3s;">一键加入Q群</button>
        <button id="closeWelcomeBtn" class="close-welcome-btn" style="background: none; border: none; color: #666; font-size: 14px; cursor: pointer; padding: 3px;">我知道了</button>
      </div>
    </div>
  </div>

  <!-- 第二个DOMContentLoaded监听器已合并到第一个中，避免重复 -->
  
  <script>
    // 加载动画函数
    function showLoading(message) {
      // 创建加载动画元素
      var loadingEl = document.createElement('div');
      loadingEl.className = 'loading-animation';
      loadingEl.id = 'loadingAnimation';
      
      // 创建并添加GIF图片
      var gifImage = document.createElement('img');
      gifImage.src = 'images/loading3.gif';
      gifImage.className = 'loading-gif';
      loadingEl.appendChild(gifImage);
      
      // 添加加载文本
      var textEl = document.createElement('div');
      textEl.className = 'loading-text';
      textEl.innerText = message || '加载中...';
      loadingEl.appendChild(textEl);
      
      // 添加到页面
      document.body.appendChild(loadingEl);
      
      // 禁止背景滚动
      document.body.style.overflow = 'hidden';
    }
    
    function hideLoading() {
      // 获取并移除加载动画
      var loadingEl = document.getElementById('loadingAnimation');
      if (loadingEl) {
        document.body.removeChild(loadingEl);
      }
      
      // 恢复滚动
      document.body.style.overflow = 'auto';
    }
  </script>
  
  <!-- 引入键盘修复脚本 -->
  <script src="keyboard_fix.js"></script>

  <script>
    // 显示使用教程二维码弹窗
    function showTutorialQRCode() {
      // 记录当前滚动位置
      var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      
      // 创建弹窗HTML
      var html = '<div class="modal-backdrop" id="tutorialModal" style="background-color: rgba(0,0,0,0.4); backdrop-filter: blur(4px); -webkit-backdrop-filter: blur(4px);">';
      html += '<div class="qrcode-container" style="text-align:center; background: linear-gradient(to bottom, #ffffff, #f8f9fa); border-radius: 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);">';
      html += '<div class="qrcode-header" style="padding: 25px 20px 10px;"><div class="qrcode-title" style="text-align:center; font-size: 24px; font-weight: 600; background: linear-gradient(135deg, #4aa1ff, #4db7e9); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; margin-bottom: 15px;">加Q群获取刷步教程</div></div>';
      
      // QQ群内容视图
      html += '<div class="qrcode-body" style="text-align:center; padding: 10px 25px 20px;">';
      html += '<div class="qrcode-img" style="margin:0 auto; display:flex; justify-content:center; width: 200px; height: 200px; padding: 10px; background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">';
      html += '<img src="images/qun1.jpg" style="width: 100%; height: 100%; border-radius: 8px;">';
      html += '</div>';
      html += '<div style="margin-top:25px; padding: 15px; background: rgba(74, 161, 255, 0.08); border-radius: 12px; width: auto; max-width: 85%; display: inline-block;">';
      html += '<div style="text-align: center; margin-bottom: 12px; color: #4aa1ff; font-weight: 600; font-size: 15px;">QQ群号：698912745</div>';
      html += '<div style="display: flex; align-items: center; margin-bottom: 12px; justify-content: center;"><i class="fas fa-check-circle" style="color: #4aa1ff; margin-right: 10px; font-size: 16px;"></i><span style="font-size:14px; color:#333;">请加官方Q群获取详细刷步教程</span></div>';
      html += '<div style="display: flex; align-items: center; justify-content: center;"><i class="fas fa-check-circle" style="color: #4aa1ff; margin-right: 10px; font-size: 16px;"></i><span style="font-size:14px; color:#333;">加群后，Q群管家自动发送教程</span></div>';
      html += '</div>';
      html += '<div class="qrcode-footer" style="text-align:center; display:flex; flex-direction:column; align-items:center; padding: 20px 20px 5px;">';
      html += '<a href="https://qm.qq.com/q/aDE0afaXsI" target="_blank" style="width: 85%; background: linear-gradient(135deg, #4aa1ff, #4db7e9); color: white; border: none; border-radius: 25px; padding: 12px 0; font-size: 16px; margin: 0 auto; cursor: pointer; display: inline-block; box-shadow: 0 5px 15px rgba(74, 161, 255, 0.2); font-weight: 500; transition: all 0.3s; text-decoration: none;">一键加群</a>';
      html += '</div>';
      html += '</div>';
      
      // 底部关闭按钮
      html += '<div class="qrcode-footer" style="text-align:center; display:flex; flex-direction:column; align-items:center; padding: 0 20px 15px;">';
      html += '<button id="closeTutorialButton" style="background: none; border: none; color: #666; font-size: 14px; cursor: pointer; padding: 8px 20px; margin-top: -10px;">关闭</button>';
      html += '</div>';
      html += '</div>';
      html += '</div>';
      
      // 直接插入HTML
      var div = document.createElement('div');
      div.innerHTML = html;
      var modal = div.firstChild;
      document.body.appendChild(modal);
      
      // 防止页面滚动
      document.body.style.overflow = 'hidden';
      // 恢复原来的滚动位置
      window.scrollTo(0, scrollTop);
      
      // 添加按钮悬浮效果
      var joinButton = modal.querySelector('a[href="https://qm.qq.com/q/aDE0afaXsI"]');
      if (joinButton) {
        joinButton.addEventListener('mouseover', function() {
          this.style.transform = 'translateY(-2px)';
          this.style.boxShadow = '0 8px 20px rgba(74, 161, 255, 0.3)';
        });
        
        joinButton.addEventListener('mouseout', function() {
          this.style.transform = 'translateY(0)';
          this.style.boxShadow = '0 5px 15px rgba(74, 161, 255, 0.2)';
        });
      }
      
      // 关闭弹窗并恢复滚动的函数
      function closeTutorialModal() {
        document.body.removeChild(modal);
        // 恢复页面滚动能力
        document.body.style.overflow = 'auto';
        // 平滑滚动到原来的位置
        window.scrollTo({
          top: scrollTop,
          behavior: 'smooth'
        });
      }
      
      // 单独注册关闭按钮的点击事件
      document.getElementById('closeTutorialButton').addEventListener('click', function(event) {
        event.preventDefault();
        closeTutorialModal();
      });
      
      // 点击背景关闭
      modal.addEventListener('click', function(e) {
        if (e.target === modal) {
          closeTutorialModal();
        }
      });
    }

    // 将showTutorialQRCode函数添加到全局作用域
    window.showTutorialQRCode = showTutorialQRCode;
  </script>

  <!-- 显示小程序二维码弹窗 -->
  <script>
    /**
     * 显示微信小程序二维码弹窗
     */
    function showMiniProgramQRCode() {
      var backdrop = document.createElement('div');
      backdrop.className = 'modal-backdrop';
      backdrop.style.backgroundColor = "rgba(0,0,0,0.4)";
      backdrop.style.backdropFilter = "blur(4px)";
      backdrop.style.webkitBackdropFilter = "blur(4px)";
      
      var modalHTML = '<div class="qrcode-container" style="padding: 25px; text-align: center; background: white; border-radius: 16px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);">';
      modalHTML += '<div style="display: flex; flex-direction: column; align-items: center; justify-content: center; color: transparent; margin-bottom: 15px;">使用微信小程序刷步</div></div>';
      modalHTML += '<h2 style="font-size: 20px; margin-bottom: 15px; color: #333;">使用微信小程序刷步</h2>';
      modalHTML += '<p style="font-size: 15px; margin-bottom: 20px; color: #666; line-height: 1.5;">扫描下方二维码，使用微信小程序获得更好体验</p>';
      modalHTML += '<div style="margin-bottom: 20px;"><img src="images/xcx.jpg" alt="小程序二维码" style="width: 180px; height: 180px; border-radius: 8px; border: 1px solid #eee;"></div>';
      modalHTML += '<button class="qrcode-btn" onclick="closeModal(this)">关闭</button>';
      modalHTML += '</div>';
      
      backdrop.innerHTML = modalHTML;
      document.body.appendChild(backdrop);
      
      // 点击背景关闭弹窗
      backdrop.addEventListener('click', function(e) {
        if (e.target === backdrop) {
          document.body.removeChild(backdrop);
        }
      });
    }
    

  </script>

  <!-- 密码显示/隐藏功能 -->
  <script>
    document.querySelector('.password-toggle-btn').addEventListener('click', function() {
      const passwordInput = document.querySelector('input[name="password"]');
      const icon = this.querySelector('i');
      
      if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
      } else {
        passwordInput.type = 'password';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
      }
    });
  </script>

  <!-- 账号切换功能脚本 -->
  <script>
    // 账号管理 - 暴露为全局对象，以便其他脚本访问
    window.AccountManager = {
      // 获取保存的账号列表
      getAccounts: function() {
        try {
          const accounts = localStorage.getItem('savedAccounts');
          return accounts ? JSON.parse(accounts) : [];
        } catch (error) {
          console.error('获取账号失败:', error);
          return [];
        }
      },
      
      // 保存账号列表
      saveAccounts: function(accounts) {
        try {
          localStorage.setItem('savedAccounts', JSON.stringify(accounts));
          return true;
        } catch (error) {
          console.error('保存账号失败:', error);
          return false;
        }
      },
      
      // 添加账号
      addAccount: function(account, password) {
        if (!account || !password) {
          return false; // 账号和密码都必须提供
        }
        
        const accounts = this.getAccounts();
        
        // 检查是否已存在相同账号
        const existingIndex = accounts.findIndex(acc => acc.account === account);
        
        if (existingIndex !== -1) {
          // 更新已存在账号的密码
          accounts[existingIndex].password = password;
        } else {
          // 添加新账号
          accounts.push({
            account: account,
            password: password,
            addTime: new Date().getTime()
          });
        }
        
        return this.saveAccounts(accounts);
      },
      
      // 删除账号
      deleteAccount: function(account) {
        if (!account) return false;
        
        let accounts = this.getAccounts();
        accounts = accounts.filter(acc => acc.account !== account);
        
        return this.saveAccounts(accounts);
      },
      
      // 清空所有账号
      clearAccounts: function() {
        return this.saveAccounts([]);
      }
    };
  
    document.addEventListener('DOMContentLoaded', function() {
      // 获取元素 - 使用var而不是const，减少作用域问题
      var accountInput = document.querySelector('input[name="user"]');
      var passwordInput = document.querySelector('input[name="password"]');
      var switchAccountBtn = document.getElementById('switchAccountBtn');
      var accountSwitchModal = document.getElementById('accountSwitchModal');
      var accountList = document.getElementById('accountList');
      var addAccountBtn = document.getElementById('addAccountBtn');
      var addAccountForm = document.getElementById('addAccountForm');
      var newAccountInput = document.getElementById('newAccountInput');
      var newPasswordInput = document.getElementById('newPasswordInput');
      var confirmAddBtn = document.getElementById('confirmAddBtn');
      var cancelAddBtn = document.getElementById('cancelAddBtn');
      var closeAccountModalBtn = document.querySelector('.account-switch-close');
      var rememberMeCheckbox = document.getElementById('rememberMe');
      
      // 显示账号切换弹窗
      function showAccountSwitchModal() {
        renderAccountList(); // 这里会设置好账号列表和底部按钮的边框样式
        accountSwitchModal.style.display = 'flex';
        // 隐藏添加账号表单
        addAccountForm.style.display = 'none';
        // 显示添加账号按钮
        addAccountBtn.style.display = 'block';
        addAccountBtnContainer.style.display = 'block';
        // 禁止背景滚动
        document.body.style.overflow = 'hidden';
      }
      
      // 关闭账号切换弹窗
      function closeAccountSwitchModal() {
        accountSwitchModal.style.display = 'none';
        // 恢复背景滚动
        document.body.style.overflow = 'auto';
        // 恢复图标
        const icon = switchAccountBtn.querySelector('i');
        icon.style.transform = 'rotate(0deg)';
        // 清空添加账号表单
        newAccountInput.value = '';
        newPasswordInput.value = '';
      }
      
      // 显示添加账号表单
      function showAddAccountForm() {
        addAccountBtn.style.display = 'none';
        addAccountForm.style.display = 'block';
        addAccountBtnContainer.style.display = 'none'; // 隐藏整个底部区域
      }
      
      // 隐藏添加账号表单
      function hideAddAccountForm() {
        addAccountForm.style.display = 'none';
        addAccountBtn.style.display = 'block';
        addAccountBtnContainer.style.display = 'block';
        // 根据账号列表情况决定底部边框
        const accounts = window.AccountManager.getAccounts();
        if (accounts.length === 0) {
          addAccountBtnContainer.style.borderTop = '1px solid #f2f2f2';
        } else {
          addAccountBtnContainer.style.borderTop = 'none';
        }
        // 清空表单
        newAccountInput.value = '';
        newPasswordInput.value = '';
      }
      
      // 渲染账号列表
      function renderAccountList() {
        const accounts = window.AccountManager.getAccounts();
        const addAccountBtnContainer = document.getElementById('addAccountBtnContainer');
        
        accountList.innerHTML = '';
        
        if (accounts.length === 0) {
          accountList.innerHTML = '<div style="text-align: center; padding: 25px 0; color: #999; font-size: 14px;">暂无已保存的账号</div>';
          // 如果没有账号，给添加按钮区域添加上边框
          addAccountBtnContainer.style.borderTop = '1px solid #f2f2f2';
          return;
        }
        
        // 如果有账号，添加淡色上边框
        addAccountBtnContainer.style.borderTop = '1px solid #f2f2f2';
        
        accounts.forEach((acc, index) => {
          const accountItem = document.createElement('div');
          accountItem.className = 'account-item';
          
          // 只有不是最后一个账号项才添加底部边框
          const borderStyle = index === accounts.length - 1 ? '' : 'border-bottom: 1px solid rgba(0, 0, 0, 0.05);';
          accountItem.style.cssText = `display: flex; align-items: center; justify-content: space-between; padding: 10px 12px; ${borderStyle} cursor: pointer; transition: background-color 0.2s;`;
          
          const accountInfo = document.createElement('div');
          accountInfo.className = 'account-info';
          accountInfo.style.cssText = 'flex: 1; display: flex; align-items: center;';
          
          // 直接使用文本容器，移除用户图标
          const textContainer = document.createElement('div');
          textContainer.style.cssText = 'flex: 1; display: flex; align-items: center; justify-content: space-between;';
          
          const accountText = document.createElement('div');
          accountText.className = 'account-text';
          accountText.style.cssText = 'font-size: 15px; font-weight: 500; color: #333; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; max-width: 170px;';
          accountText.textContent = acc.account;
          
          const switchText = document.createElement('div');
          switchText.className = 'switch-text';
          switchText.style.cssText = 'font-size: 12px; color: #4aa1ff; margin-left: 10px; padding: 2px 8px; background-color: rgba(74, 161, 255, 0.05); border-radius: 10px; border: 1px solid rgba(74, 161, 255, 0.2); box-shadow: 0 1px 2px rgba(74, 161, 255, 0.05);';
          switchText.textContent = '切换';
          
          textContainer.appendChild(accountText);
          textContainer.appendChild(switchText);
          
          accountInfo.appendChild(textContainer);
          
          const deleteBtn = document.createElement('button');
          deleteBtn.className = 'delete-account-btn';
          deleteBtn.style.cssText = 'background: none; border: none; color: #ff4d4f; cursor: pointer; padding: 8px; margin-left: 10px; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; transition: background-color 0.2s;';
          deleteBtn.innerHTML = '<i class="fas fa-trash-alt" style="font-size: 14px;"></i>';
          
          // 添加悬浮效果
          deleteBtn.addEventListener('mouseover', function() {
            this.style.backgroundColor = '#ffecec';
          });
          
          deleteBtn.addEventListener('mouseout', function() {
            this.style.backgroundColor = 'transparent';
          });
          
          accountItem.addEventListener('mouseover', function() {
            this.style.backgroundColor = '#f0f2f5';
          });
          
          accountItem.addEventListener('mouseout', function() {
            this.style.backgroundColor = 'transparent';
          });
          
          // 点击账号切换事件
          accountInfo.addEventListener('click', function() {
            accountInput.value = acc.account;
            passwordInput.value = acc.password;
            rememberMeCheckbox.checked = true;
            closeAccountSwitchModal();
          });
          
          // 点击删除按钮事件
          deleteBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            if (confirm('确定要删除账号 ' + acc.account + ' 吗？')) {
              window.AccountManager.deleteAccount(acc.account);
              renderAccountList();
            }
          });
          
          accountItem.appendChild(accountInfo);
          accountItem.appendChild(deleteBtn);
          accountList.appendChild(accountItem);
        });
      }
      
      // 点击切换账号按钮
      switchAccountBtn.addEventListener('click', function() {
        // 添加旋转动画
        const icon = this.querySelector('i');
        icon.style.transform = icon.style.transform === 'rotate(180deg)' ? 'rotate(0deg)' : 'rotate(180deg)';
        
        showAccountSwitchModal();
      });
      
      // 点击添加新账号按钮
      addAccountBtn.addEventListener('click', function() {
        showAddAccountForm();
      });
      
      // 点击取消添加按钮
      cancelAddBtn.addEventListener('click', function() {
        hideAddAccountForm();
      });
      
      // 点击确认添加按钮
      confirmAddBtn.addEventListener('click', function() {
        const account = newAccountInput.value.trim();
        const password = newPasswordInput.value;
        
        if (!account) {
          alert('请输入账号');
          return;
        }
        
        if (!password) {
          alert('请输入密码');
          return;
        }
        
        // 验证账号格式
        const emailOrPhoneRegex = /^(?:[^\s@]+@[^\s@]+\.[^\s@]+$|1[3-9]\d{9})$/;
        if (!emailOrPhoneRegex.test(account)) {
          alert('请输入有效的邮箱地址或手机号');
          return;
        }
        
        // 添加账号
        if (window.AccountManager.addAccount(account, password)) {
          // 重新渲染账号列表
          renderAccountList();
          // 隐藏添加表单
          hideAddAccountForm();
          // 提示用户
          alert('账号添加成功');
        } else {
          alert('账号添加失败，请重试');
        }
      });
      
      // 关闭弹窗按钮点击事件
      closeAccountModalBtn.addEventListener('click', closeAccountSwitchModal);
      
      // 点击弹窗外部关闭弹窗
      accountSwitchModal.addEventListener('click', function(e) {
        if (e.target === accountSwitchModal) {
          closeAccountSwitchModal();
        }
      });
      
      // 不再需要监听表单提交成功事件，我们将修改原始的saveUserCredentials函数
    });
  </script>
  
  <!-- 修改原始的账号密码保存函数，集成账号切换功能 -->
  <script>
    // 在页面完全加载后扩展原有的保存凭据功能
    document.addEventListener('DOMContentLoaded', function() {
      // 直接添加一个监听函数，在表单提交成功时保存账号到切换列表
      var saveAccountToSwitchList = function() {
        try {
          // 获取表单数据
          var rememberMe = document.getElementById('rememberMe').checked;
          var user = document.querySelector('input[name="user"]').value;
          var password = document.querySelector('input[name="password"]').value;
          
          // 如果勾选了"记住我"且提供了账号密码
          if (rememberMe && user && password && window.AccountManager) {
            window.AccountManager.addAccount(user, password);
            console.log('账号已添加到切换列表');
          }
        } catch (e) {
          console.error('保存账号到切换列表失败:', e);
        }
      };
      
      // 修改showModal函数，在显示成功结果后调用我们的函数
      if (typeof window.showModal === 'function') {
        // 保存原始函数引用
        var originalShowModal = window.showModal;
        
                 // 覆盖showModal函数
         window.showModal = function(type, title, content, btnText) {

           // 如果是成功提交的弹窗，则保存账号
           if ((type === 'success' || type === '成功') && content && content.includes('已成功修改步数')) {
             saveAccountToSwitchList();
           }
          
          // 调用原始showModal函数显示弹窗
          return originalShowModal.apply(this, arguments);
        };
      }
    });
  </script>

  <!-- 显示小程序二维码弹窗 -->
  <script>
    function showMiniProgramQRCode() {
      // 记录当前滚动位置
      var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      
      // 创建弹窗HTML
      var html = '<div class="modal-backdrop" id="miniProgramModal" style="background-color: rgba(0,0,0,0.4); backdrop-filter: blur(4px); -webkit-backdrop-filter: blur(4px);">';
      html += '<div class="qrcode-container" style="text-align:center; background: linear-gradient(to bottom, #ffffff, #f8f9fa); border-radius: 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);">';
      html += '<div class="qrcode-header" style="padding: 25px 20px 10px;"><div class="qrcode-title" style="text-align:center; font-size: 24px; font-weight: 600; background: linear-gradient(135deg, #4aa1ff, #4db7e9); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; margin-bottom: 15px;">微信小程序版</div></div>';
      
      // 添加选项卡导航
      html += '<div class="tabs-nav" style="display: flex; justify-content: center; margin-bottom: 20px; border-bottom: 1px solid #eee; padding: 0 20px;">';
      html += '<div class="tab-item active" data-tab="qrcode" style="padding: 10px 20px; cursor: pointer; font-size: 15px; font-weight: 500; color: #4aa1ff; border-bottom: 2px solid #4aa1ff; margin: 0 10px;">扫码进入</div>';
      html += '<div class="tab-item" data-tab="tutorial" style="padding: 10px 20px; cursor: pointer; font-size: 15px; font-weight: 500; color: #666; margin: 0 10px;">小程序教程</div>';
      html += '</div>';
      
      // 二维码选项卡内容
      html += '<div class="tab-content" id="qrcode-tab" style="display: block;">';
      html += '<div class="qrcode-body" style="text-align:center; padding: 10px 25px 20px;">';
      html += '<div class="qrcode-img" style="margin:0 auto; display:flex; justify-content:center; width: 200px; height: 200px; padding: 10px; background: white; border-radius: 15px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">';
      html += '<img src="images/xcx2.jpg" style="width: 100%; height: 100%; border-radius: 8px;">';
      html += '</div>';
      html += '<div style="margin-top:25px; padding: 15px; background: rgba(74, 161, 255, 0.08); border-radius: 12px; width: 85%; margin-left: auto; margin-right: auto;">';
      html += '<div style="display: flex; align-items: center; margin-bottom: 12px; justify-content: center;"><i class="fas fa-check-circle" style="color: #4aa1ff; margin-right: 10px; font-size: 16px;"></i><span style="font-size:14px; color:#333;">截图到微信扫码即可打开小程序</span></div>';
      html += '<div style="display: flex; align-items: center; justify-content: center;"><i class="fas fa-check-circle" style="color: #4aa1ff; margin-right: 10px; font-size: 16px;"></i><span style="font-size:14px; color:#333;">小程序支持非会员看广告免费使用</span></div>';
      html += '</div>';
      html += '</div>';
      html += '</div>';
      
      // 教程选项卡内容
      html += '<div class="tab-content" id="tutorial-tab" style="display: none; padding: 10px 25px 20px;">';
      
      // 步骤1
      html += '<div class="tutorial-step" style="background: rgba(74, 161, 255, 0.05); border-radius: 12px; padding: 15px; margin-bottom: 15px; text-align: left;">';
      html += '<div class="step-header" style="display: flex; align-items: center; margin-bottom: 10px;">';
      html += '</div>';
      html += '<div class="step-content" style="padding-left: 38px; font-size: 14px; color: #555; line-height: 1.5;">';
      html += '<p style="margin: 0 0 5px;">1、请先学会如何使用本站的网页版，</p>';
      html += '<p style="margin: 0 0 5px;">2、学会后再用小程序也是类似操作。</p>';
      html += '<p style="margin: 0;">3、小程序内的部分功能表述不重要。</p>';
      html += '</div>';
      html += '</div>';
      
      
      html += '</div>';
      
      // 底部按钮
      html += '<div class="qrcode-footer" style="text-align:center; display:flex; flex-direction:column; align-items:center; padding: 0 20px 25px;">';
      html += '<button id="closeMiniProgramButton" style="width: 85%; background: linear-gradient(135deg, #4aa1ff, #4db7e9); color: white; border: none; border-radius: 25px; padding: 12px 0; font-size: 16px; margin: 5px auto; cursor: pointer; display: inline-block; box-shadow: 0 5px 15px rgba(74, 161, 255, 0.2); font-weight: 500; transition: all 0.3s;">关闭</button>';
      html += '</div>';
      html += '</div>';
      html += '</div>';
      
      // 直接插入HTML
      var div = document.createElement('div');
      div.innerHTML = html;
      var modal = div.firstChild;
      document.body.appendChild(modal);
      
      // 防止页面滚动
      document.body.style.overflow = 'hidden';
      // 恢复原来的滚动位置
      window.scrollTo(0, scrollTop);
      
      // 添加按钮悬浮效果
      var closeButton = document.getElementById('closeMiniProgramButton');
      if (closeButton) {
        closeButton.addEventListener('mouseover', function() {
          this.style.transform = 'translateY(-2px)';
          this.style.boxShadow = '0 8px 20px rgba(74, 161, 255, 0.3)';
        });
        
        closeButton.addEventListener('mouseout', function() {
          this.style.transform = 'translateY(0)';
          this.style.boxShadow = '0 5px 15px rgba(74, 161, 255, 0.2)';
        });
      }
      
      // 实现选项卡切换功能
      var tabItems = modal.querySelectorAll('.tab-item');
      tabItems.forEach(function(tab) {
        tab.addEventListener('click', function() {
          // 移除所有tab的激活状态
          tabItems.forEach(function(item) {
            item.style.color = '#666';
            item.style.borderBottom = 'none';
            item.classList.remove('active');
          });
          
          // 添加当前tab的激活状态
          this.style.color = '#4aa1ff';
          this.style.borderBottom = '2px solid #4aa1ff';
          this.classList.add('active');
          
          // 隐藏所有内容
          var contents = modal.querySelectorAll('.tab-content');
          contents.forEach(function(content) {
            content.style.display = 'none';
          });
          
          // 显示当前内容
          var tabName = this.getAttribute('data-tab');
          document.getElementById(tabName + '-tab').style.display = 'block';
        });
      });
      
      // 关闭弹窗并恢复滚动的函数
      function closeMiniProgramModal() {
        document.body.removeChild(modal);
        // 恢复页面滚动能力
        document.body.style.overflow = 'auto';
        // 平滑滚动到原来的位置
        window.scrollTo({
          top: scrollTop,
          behavior: 'smooth'
        });
      }
      
      // 单独注册关闭按钮的点击事件
      document.getElementById('closeMiniProgramButton').addEventListener('click', function(event) {
        event.preventDefault();
        closeMiniProgramModal();
      });
      
      // 点击背景关闭
      modal.addEventListener('click', function(e) {
        if (e.target === modal) {
          closeMiniProgramModal();
        }
      });
    }

    // 将showMiniProgramQRCode函数添加到全局作用域
    window.showMiniProgramQRCode = showMiniProgramQRCode;
  </script>
</body>
</html>
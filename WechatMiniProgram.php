<?php
/**
 * 微信小程序跳转类
 * 支持明文URL Scheme、加密URL Scheme、加密URL Link三种方式
 */

class WechatMiniProgram {
    private $appid;
    private $appSecret;
    private $accessToken;
    
    public function __construct($appid, $appSecret) {
        $this->appid = $appid;
        $this->appSecret = $appSecret;
    }
    
    /**
     * 获取access_token
     */
    private function getAccessToken() {
        if ($this->accessToken) {
            return $this->accessToken;
        }

        // 先尝试从缓存获取
        $cachedToken = getCachedAccessToken();
        if ($cachedToken) {
            $this->accessToken = $cachedToken;
            return $this->accessToken;
        }

        // 缓存中没有，优先使用稳定版access_token
        try {
            $this->accessToken = getStableAccessToken($this->appid, $this->appSecret);
            return $this->accessToken;
        } catch (Exception $e) {
            writeLog("Failed to get stable access token: " . $e->getMessage(), 'WARNING');

            // 稳定版失败，尝试普通版本
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appid}&secret={$this->appSecret}";
            $response = $this->httpGet($url);
            $data = json_decode($response, true);

            if (isset($data['access_token'])) {
                $this->accessToken = $data['access_token'];
                cacheAccessToken($data['access_token'], $data['expires_in'] ?? 7200, false);
                writeLog("Successfully obtained regular access token as fallback");
                return $this->accessToken;
            }

            $errorMsg = '获取access_token失败: ';
            if (isset($data['errcode'])) {
                $errorMsg .= getErrorMessage($data['errcode']);
            } else {
                $errorMsg .= $response;
            }
            writeLog($errorMsg, 'ERROR');
            throw new Exception($errorMsg);
        }
    }
    
    /**
     * 生成明文URL Scheme
     * @param string $path 小程序页面路径
     * @param array $query 查询参数
     * @param string $envVersion 版本类型 release|trial|develop
     */
    public function generatePlainUrlScheme($path = '', $query = [], $envVersion = 'release') {
        $queryString = '';
        if (!empty($query)) {
            $queryString = is_array($query) ? http_build_query($query) : $query;
        }
        
        $scheme = "weixin://dl/business/?appid={$this->appid}";
        if ($path) {
            $scheme .= "&path=" . urlencode($path);
        }
        if ($queryString) {
            $scheme .= "&query=" . urlencode($queryString);
        }
        $scheme .= "&env_version={$envVersion}";
        
        return $scheme;
    }
    
    /**
     * 生成加密URL Scheme
     * @param string $path 小程序页面路径
     * @param array $query 查询参数
     * @param string $envVersion 版本类型
     */
    public function generateEncryptedUrlScheme($path = '', $query = [], $envVersion = 'release') {
        $accessToken = $this->getAccessToken();
        $url = "https://api.weixin.qq.com/wxa/generatescheme?access_token={$accessToken}";
        
        $queryString = is_array($query) ? http_build_query($query) : $query;
        
        $data = [
            'jump_wxa' => [
                'path' => $path,
                'query' => $queryString,
                'env_version' => $envVersion
            ],
            'is_expire' => false
        ];
        
        $response = $this->httpPost($url, json_encode($data));
        $result = json_decode($response, true);
        
        if (isset($result['openlink'])) {
            return $result['openlink'];
        }
        
        throw new Exception('生成加密URL Scheme失败: ' . (isset($result['errcode']) ? getErrorMessage($result['errcode']) : $response));
    }
    
    /**
     * 生成加密URL Link
     * @param string $path 小程序页面路径
     * @param array $query 查询参数
     * @param string $envVersion 版本类型
     */
    public function generateEncryptedUrlLink($path = '', $query = [], $envVersion = 'release') {
        $accessToken = $this->getAccessToken();
        $url = "https://api.weixin.qq.com/wxa/generate_urllink?access_token={$accessToken}";
        
        $queryString = is_array($query) ? http_build_query($query) : $query;
        
        $data = [
            'path' => $path,
            'query' => $queryString,
            'env_version' => $envVersion,
            'is_expire' => false
        ];
        
        $response = $this->httpPost($url, json_encode($data));
        $result = json_decode($response, true);
        
        if (isset($result['url_link'])) {
            return $result['url_link'];
        }
        
        throw new Exception('生成加密URL Link失败: ' . (isset($result['errcode']) ? getErrorMessage($result['errcode']) : $response));
    }
    
    /**
     * HTTP GET请求
     */
    private function httpGet($url) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("HTTP请求失败，状态码: {$httpCode}");
        }
        
        return $response;
    }
    
    /**
     * HTTP POST请求
     */
    private function httpPost($url, $data) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("HTTP请求失败，状态码: {$httpCode}");
        }
        
        return $response;
    }
}
?>

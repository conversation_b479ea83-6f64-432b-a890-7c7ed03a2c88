<?php
/**
 * 微信小程序跳转API接口
 * 提供JSON格式的API调用方式
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once 'config.php';
require_once 'WechatMiniProgram.php';

// 统一响应格式
function apiResponse($success, $data = null, $message = '', $code = 0) {
    return json_encode([
        'success' => $success,
        'code' => $code,
        'message' => $message,
        'data' => $data,
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
}

// 处理API请求
try {
    $method = $_SERVER['REQUEST_METHOD'];
    $input = null;
    
    if ($method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
    } elseif ($method === 'GET') {
        $input = $_GET;
    } else {
        throw new Exception('不支持的请求方法');
    }
    
    if (!$input) {
        throw new Exception('请求参数为空');
    }
    
    // 验证必需参数
    $type = $input['type'] ?? 'plain';
    $path = $input['path'] ?? '';
    $query = $input['query'] ?? [];
    $envVersion = $input['env_version'] ?? 'release';
    
    // 如果query是字符串，尝试解析
    if (is_string($query) && !empty($query)) {
        parse_str($query, $parsedQuery);
        $query = $parsedQuery;
    }
    
    $wechat = new WechatMiniProgram(WECHAT_APPID, WECHAT_APP_SECRET);
    $result = [];
    
    switch ($type) {
        case 'plain':
            $result['plain_scheme'] = $wechat->generatePlainUrlScheme($path, $query, $envVersion);
            break;
            
        case 'encrypted_scheme':
            $result['encrypted_scheme'] = $wechat->generateEncryptedUrlScheme($path, $query, $envVersion);
            break;
            
        case 'encrypted_link':
            $result['encrypted_link'] = $wechat->generateEncryptedUrlLink($path, $query, $envVersion);
            break;
            
        case 'all':
            $result['plain_scheme'] = $wechat->generatePlainUrlScheme($path, $query, $envVersion);
            $result['encrypted_scheme'] = $wechat->generateEncryptedUrlScheme($path, $query, $envVersion);
            $result['encrypted_link'] = $wechat->generateEncryptedUrlLink($path, $query, $envVersion);
            break;
            
        default:
            throw new Exception('不支持的类型: ' . $type);
    }
    
    writeLog("API: Successfully generated {$type} for path: {$path}");
    echo apiResponse(true, $result, '生成成功');
    
} catch (Exception $e) {
    writeLog("API Error: " . $e->getMessage(), 'ERROR');
    http_response_code(400);
    echo apiResponse(false, null, $e->getMessage(), 400);
}
?>
